<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1753022438370" clover="3.2.0">
  <project timestamp="1753022438371" name="All files">
    <metrics statements="7454" coveredstatements="492" conditionals="4243" coveredconditionals="269" methods="1714" coveredmethods="89" elements="13411" coveredelements="850" complexity="0" loc="7454" ncloc="7454" packages="13" files="62" classes="62"/>
    <package name="src">
      <metrics statements="61" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="16" coveredmethods="0"/>
      <file name="background.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\background.js">
        <metrics statements="61" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.api">
      <metrics statements="302" coveredstatements="127" conditionals="199" coveredconditionals="70" methods="41" coveredmethods="12"/>
      <file name="tab-api.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\api\tab-api.js">
        <metrics statements="157" coveredstatements="127" conditionals="111" coveredconditionals="70" methods="12" coveredmethods="12"/>
        <line num="6" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="24" count="39" type="stmt"/>
        <line num="25" count="39" type="stmt"/>
        <line num="26" count="39" type="stmt"/>
        <line num="27" count="39" type="stmt"/>
        <line num="34" count="39" type="cond" truecount="1" falsecount="1"/>
        <line num="36" count="39" type="stmt"/>
        <line num="37" count="39" type="cond" truecount="1" falsecount="1"/>
        <line num="39" count="39" type="stmt"/>
        <line num="40" count="39" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="39" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="175" type="stmt"/>
        <line num="58" count="175" type="stmt"/>
        <line num="61" count="168" type="stmt"/>
        <line num="62" count="168" type="stmt"/>
        <line num="79" count="168" type="stmt"/>
        <line num="82" count="168" type="cond" truecount="1" falsecount="1"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="168" type="cond" truecount="1" falsecount="1"/>
        <line num="88" count="168" type="stmt"/>
        <line num="92" count="168" type="stmt"/>
        <line num="95" count="168" type="stmt"/>
        <line num="101" count="168" type="stmt"/>
        <line num="103" count="7" type="cond" truecount="2" falsecount="3"/>
        <line num="106" count="7" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="119" count="15" type="stmt"/>
        <line num="121" count="15" type="stmt"/>
        <line num="124" count="14" type="stmt"/>
        <line num="125" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="126" count="1" type="stmt"/>
        <line num="130" count="13" type="stmt"/>
        <line num="136" count="13" type="cond" truecount="2" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="13" type="stmt"/>
        <line num="144" count="13" type="stmt"/>
        <line num="147" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="148" count="13" type="stmt"/>
        <line num="152" count="13" type="stmt"/>
        <line num="155" count="13" type="stmt"/>
        <line num="164" count="13" type="stmt"/>
        <line num="166" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="168" count="2" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="180" count="10" type="stmt"/>
        <line num="182" count="10" type="stmt"/>
        <line num="183" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="184" count="1" type="stmt"/>
        <line num="188" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="189" count="9" type="stmt"/>
        <line num="193" count="9" type="stmt"/>
        <line num="196" count="9" type="stmt"/>
        <line num="202" count="9" type="stmt"/>
        <line num="204" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="205" count="1" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="217" count="2" type="stmt"/>
        <line num="226" count="18" type="stmt"/>
        <line num="228" count="18" type="stmt"/>
        <line num="231" count="18" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="246" count="18" type="cond" truecount="3" falsecount="1"/>
        <line num="247" count="0" type="stmt"/>
        <line num="251" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="252" count="1" type="stmt"/>
        <line num="255" count="17" type="stmt"/>
        <line num="256" count="17" type="stmt"/>
        <line num="258" count="17" type="stmt"/>
        <line num="260" count="17" type="cond" truecount="3" falsecount="1"/>
        <line num="261" count="17" type="stmt"/>
        <line num="265" count="17" type="stmt"/>
        <line num="266" count="145" type="stmt"/>
        <line num="267" count="145" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="273" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="274" count="1" type="stmt"/>
        <line num="278" count="16" type="stmt"/>
        <line num="279" count="143" type="stmt"/>
        <line num="280" count="143" type="stmt"/>
        <line num="284" count="16" type="cond" truecount="3" falsecount="1"/>
        <line num="285" count="16" type="stmt"/>
        <line num="289" count="16" type="stmt"/>
        <line num="295" count="16" type="stmt"/>
        <line num="298" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="299" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="317" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="323" count="0" type="stmt"/>
        <line num="326" count="3" type="stmt"/>
        <line num="327" count="3" type="stmt"/>
        <line num="329" count="3" type="stmt"/>
        <line num="331" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="332" count="3" type="stmt"/>
        <line num="336" count="3" type="stmt"/>
        <line num="337" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="338" count="0" type="stmt"/>
        <line num="341" count="6" type="stmt"/>
        <line num="342" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="6" type="stmt"/>
        <line num="347" count="6" type="stmt"/>
        <line num="351" count="3" type="stmt"/>
        <line num="352" count="6" type="stmt"/>
        <line num="353" count="6" type="stmt"/>
        <line num="357" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="358" count="3" type="stmt"/>
        <line num="362" count="3" type="stmt"/>
        <line num="368" count="3" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="372" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="390" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="396" count="0" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="403" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="404" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="2" type="stmt"/>
        <line num="410" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="2" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="2" type="stmt"/>
        <line num="422" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="423" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="433" count="1" type="stmt"/>
        <line num="436" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="437" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="454" count="2" type="stmt"/>
        <line num="461" count="1" type="stmt"/>
        <line num="462" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="463" count="1" type="stmt"/>
        <line num="466" count="1" type="stmt"/>
        <line num="468" count="1" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="479" count="1" type="stmt"/>
      </file>
      <file name="window-api.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\api\window-api.js">
        <metrics statements="145" coveredstatements="0" conditionals="88" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="455" coveredstatements="0" conditionals="292" coveredconditionals="0" methods="122" coveredmethods="0"/>
      <file name="settings-panel.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\components\settings-panel.js">
        <metrics statements="114" coveredstatements="0" conditionals="80" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="235" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="329" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="346" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="442" count="0" type="stmt"/>
        <line num="444" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="445" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="452" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="453" count="0" type="stmt"/>
      </file>
      <file name="tab-card.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\components\tab-card.js">
        <metrics statements="83" coveredstatements="0" conditionals="70" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="275" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="297" count="0" type="stmt"/>
      </file>
      <file name="toolbar.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\components\toolbar.js">
        <metrics statements="149" coveredstatements="0" conditionals="72" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="267" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="376" count="0" type="stmt"/>
      </file>
      <file name="window-list.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\components\window-list.js">
        <metrics statements="109" coveredstatements="0" conditionals="70" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="276" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.content">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="content.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\content\content.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.core">
      <metrics statements="552" coveredstatements="0" conditionals="272" coveredconditionals="0" methods="150" coveredmethods="0"/>
      <file name="error-handler.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\error-handler.js">
        <metrics statements="76" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="146" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
      </file>
      <file name="event-bus.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\event-bus.js">
        <metrics statements="53" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="event-emitter.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\event-emitter.js">
        <metrics statements="62" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
      </file>
      <file name="message-handler.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\message-handler.js">
        <metrics statements="113" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="31" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
      </file>
      <file name="performance-monitor.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\performance-monitor.js">
        <metrics statements="89" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="222" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="233" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
      </file>
      <file name="state-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\core\state-manager.js">
        <metrics statements="159" coveredstatements="0" conditionals="91" coveredconditionals="0" methods="35" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.models">
      <metrics statements="151" coveredstatements="0" conditionals="199" coveredconditionals="0" methods="35" coveredmethods="0"/>
      <file name="settings-model.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\models\settings-model.js">
        <metrics statements="54" coveredstatements="0" conditionals="81" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
      </file>
      <file name="tab-model.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\models\tab-model.js">
        <metrics statements="42" coveredstatements="0" conditionals="53" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
      </file>
      <file name="window-model.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\models\window-model.js">
        <metrics statements="55" coveredstatements="0" conditionals="65" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="src.modules">
      <metrics statements="1215" coveredstatements="120" conditionals="592" coveredconditionals="20" methods="259" coveredmethods="30"/>
      <file name="batch-operations.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\batch-operations.js">
        <metrics statements="141" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="41" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="518" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="532" count="0" type="stmt"/>
        <line num="540" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
      </file>
      <file name="import-export-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\import-export-manager.js">
        <metrics statements="128" coveredstatements="0" conditionals="39" coveredconditionals="0" methods="27" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="340" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
      </file>
      <file name="search-filter.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\search-filter.js">
        <metrics statements="120" coveredstatements="0" conditionals="64" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="403" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="409" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="430" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="445" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="446" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="471" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
      </file>
      <file name="storage-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\storage-manager.js">
        <metrics statements="371" coveredstatements="0" conditionals="213" coveredconditionals="0" methods="58" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="274" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="413" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="447" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="448" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="472" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="473" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="499" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="500" count="0" type="stmt"/>
        <line num="505" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="506" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="514" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="515" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="516" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="573" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="596" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="597" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="609" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="610" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="621" count="0" type="stmt"/>
        <line num="626" count="0" type="stmt"/>
        <line num="627" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="628" count="0" type="stmt"/>
        <line num="631" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="632" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="676" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="691" count="0" type="stmt"/>
        <line num="699" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="700" count="0" type="stmt"/>
        <line num="704" count="0" type="stmt"/>
        <line num="705" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="706" count="0" type="stmt"/>
        <line num="714" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="715" count="0" type="stmt"/>
        <line num="719" count="0" type="stmt"/>
        <line num="720" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="732" count="0" type="stmt"/>
        <line num="733" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="734" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="735" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="737" count="0" type="stmt"/>
        <line num="742" count="0" type="stmt"/>
        <line num="749" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="750" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="756" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="758" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="759" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="760" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="761" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="762" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="763" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="764" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="765" count="0" type="stmt"/>
        <line num="766" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="770" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="774" count="0" type="stmt"/>
        <line num="781" count="0" type="stmt"/>
        <line num="782" count="0" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="785" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="786" count="0" type="stmt"/>
        <line num="788" count="0" type="stmt"/>
        <line num="792" count="0" type="stmt"/>
        <line num="799" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="802" count="0" type="stmt"/>
        <line num="810" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="812" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="814" count="0" type="stmt"/>
        <line num="816" count="0" type="stmt"/>
        <line num="823" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="839" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="870" count="0" type="stmt"/>
        <line num="871" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="880" count="0" type="stmt"/>
        <line num="890" count="0" type="stmt"/>
        <line num="897" count="0" type="stmt"/>
        <line num="904" count="0" type="stmt"/>
        <line num="906" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="907" count="0" type="stmt"/>
        <line num="910" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="911" count="0" type="stmt"/>
        <line num="914" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="915" count="0" type="stmt"/>
        <line num="918" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="920" count="0" type="stmt"/>
        <line num="922" count="0" type="stmt"/>
        <line num="930" count="0" type="stmt"/>
        <line num="932" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="933" count="0" type="stmt"/>
        <line num="934" count="0" type="stmt"/>
        <line num="935" count="0" type="stmt"/>
        <line num="937" count="0" type="stmt"/>
        <line num="941" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="942" count="0" type="stmt"/>
        <line num="943" count="0" type="stmt"/>
        <line num="944" count="0" type="stmt"/>
        <line num="946" count="0" type="stmt"/>
        <line num="950" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="951" count="0" type="stmt"/>
        <line num="952" count="0" type="stmt"/>
        <line num="953" count="0" type="stmt"/>
        <line num="955" count="0" type="stmt"/>
        <line num="959" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="960" count="0" type="stmt"/>
        <line num="964" count="0" type="stmt"/>
        <line num="972" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="973" count="0" type="stmt"/>
        <line num="980" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="981" count="0" type="stmt"/>
        <line num="988" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="989" count="0" type="stmt"/>
        <line num="996" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1003" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1004" count="0" type="stmt"/>
        <line num="1005" count="0" type="stmt"/>
        <line num="1012" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1013" count="0" type="stmt"/>
        <line num="1020" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1021" count="0" type="stmt"/>
        <line num="1022" count="0" type="stmt"/>
        <line num="1029" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1030" count="0" type="stmt"/>
        <line num="1031" count="0" type="stmt"/>
        <line num="1038" count="0" type="stmt"/>
        <line num="1046" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1047" count="0" type="stmt"/>
        <line num="1051" count="0" type="stmt"/>
        <line num="1052" count="0" type="stmt"/>
        <line num="1053" count="0" type="stmt"/>
        <line num="1056" count="0" type="stmt"/>
        <line num="1057" count="0" type="stmt"/>
        <line num="1059" count="0" type="stmt"/>
        <line num="1064" count="0" type="stmt"/>
      </file>
      <file name="tab-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\tab-manager.js">
        <metrics statements="147" coveredstatements="120" conditionals="40" coveredconditionals="20" methods="33" coveredmethods="30"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="16" count="39" type="stmt"/>
        <line num="17" count="39" type="stmt"/>
        <line num="18" count="39" type="stmt"/>
        <line num="21" count="39" type="stmt"/>
        <line num="27" count="39" type="stmt"/>
        <line num="28" count="39" type="stmt"/>
        <line num="35" count="39" type="cond" truecount="1" falsecount="1"/>
        <line num="37" count="39" type="stmt"/>
        <line num="38" count="39" type="stmt"/>
        <line num="39" count="39" type="stmt"/>
        <line num="42" count="39" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="54" count="32" type="stmt"/>
        <line num="56" count="32" type="stmt"/>
        <line num="59" count="25" type="stmt"/>
        <line num="61" count="25" type="stmt"/>
        <line num="71" count="9" type="stmt"/>
        <line num="73" count="9" type="stmt"/>
        <line num="76" count="7" type="stmt"/>
        <line num="78" count="7" type="stmt"/>
        <line num="87" count="8" type="stmt"/>
        <line num="89" count="8" type="stmt"/>
        <line num="92" count="7" type="stmt"/>
        <line num="94" count="7" type="stmt"/>
        <line num="103" count="6" type="stmt"/>
        <line num="106" count="6" type="stmt"/>
        <line num="107" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="108" count="4" type="stmt"/>
        <line num="112" count="2" type="stmt"/>
        <line num="115" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
        <line num="128" count="18" type="stmt"/>
        <line num="131" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="139" count="18" type="stmt"/>
        <line num="142" count="18" type="stmt"/>
        <line num="143" count="31" type="stmt"/>
        <line num="146" count="18" type="stmt"/>
        <line num="155" count="18" type="stmt"/>
        <line num="157" count="18" type="stmt"/>
        <line num="160" count="16" type="stmt"/>
        <line num="161" count="143" type="stmt"/>
        <line num="164" count="16" type="stmt"/>
        <line num="173" count="3" type="stmt"/>
        <line num="175" count="3" type="stmt"/>
        <line num="178" count="3" type="stmt"/>
        <line num="179" count="6" type="stmt"/>
        <line num="182" count="3" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="2" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="215" count="0" type="stmt"/>
        <line num="219" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="234" count="1" type="stmt"/>
        <line num="242" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="243" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="3" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="269" count="0" type="stmt"/>
        <line num="274" count="3" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="301" count="2" type="stmt"/>
        <line num="303" count="2" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="317" count="0" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="3" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="3" type="stmt"/>
        <line num="360" count="6" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="383" count="3" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="410" count="39" type="cond" truecount="1" falsecount="1"/>
        <line num="411" count="39" type="stmt"/>
        <line num="414" count="39" type="stmt"/>
        <line num="415" count="39" type="stmt"/>
        <line num="416" count="39" type="stmt"/>
        <line num="426" count="105" type="cond" truecount="1" falsecount="1"/>
        <line num="427" count="0" type="stmt"/>
        <line num="437" count="212" type="cond" truecount="2" falsecount="2"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="444" count="212" type="stmt"/>
        <line num="445" count="212" type="stmt"/>
        <line num="453" count="6" type="stmt"/>
        <line num="454" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="457" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="463" count="4" type="stmt"/>
        <line num="471" count="9" type="stmt"/>
        <line num="472" count="9" type="stmt"/>
        <line num="481" count="18" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="498" count="39" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="504" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="516" count="1" type="stmt"/>
      </file>
      <file name="tab-operations.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\tab-operations.js">
        <metrics statements="164" coveredstatements="0" conditionals="89" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="314" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="337" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="338" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="390" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="481" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="488" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="489" count="0" type="stmt"/>
        <line num="493" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="494" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="520" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="521" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="532" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="552" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="window-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\modules\window-manager.js">
        <metrics statements="144" coveredstatements="0" conditionals="109" coveredconditionals="0" methods="36" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="313" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="356" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="398" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.options">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="options.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\options\options.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages">
      <metrics statements="250" coveredstatements="0" conditionals="86" coveredconditionals="0" methods="97" coveredmethods="0"/>
      <file name="management.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\pages\management.js">
        <metrics statements="250" coveredstatements="0" conditionals="86" coveredconditionals="0" methods="97" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="358" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="426" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="456" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="471" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="472" count="0" type="stmt"/>
        <line num="476" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="477" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="492" count="0" type="stmt"/>
        <line num="495" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="522" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="523" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="546" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="550" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="620" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="621" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="630" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="631" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="632" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="634" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="635" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="638" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="657" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="658" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="665" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="stmt"/>
        <line num="687" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="688" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="699" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="700" count="0" type="stmt"/>
        <line num="702" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="708" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="709" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="714" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.popup">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="popup.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\popup\popup.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.shared">
      <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="constants.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\shared\constants.js">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="4382" coveredstatements="184" conditionals="2508" coveredconditionals="113" methods="972" coveredmethods="26"/>
      <file name="backup-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\backup-manager.js">
        <metrics statements="187" coveredstatements="0" conditionals="78" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="309" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="421" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="427" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="449" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="450" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="510" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="511" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="512" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="515" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="516" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="517" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="529" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="553" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="554" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="559" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="561" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
      </file>
      <file name="batch-executor.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\batch-executor.js">
        <metrics statements="201" coveredstatements="0" conditionals="76" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="375" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="481" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="482" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="490" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="499" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="520" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="550" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="560" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="570" count="0" type="stmt"/>
        <line num="579" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="580" count="0" type="stmt"/>
        <line num="582" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="583" count="0" type="stmt"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="586" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
      </file>
      <file name="batch-processor.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\batch-processor.js">
        <metrics statements="115" coveredstatements="0" conditionals="78" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="174" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
      </file>
      <file name="browser-importer.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\browser-importer.js">
        <metrics statements="88" coveredstatements="0" conditionals="67" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="293" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="297" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
      </file>
      <file name="cache-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\cache-manager.js">
        <metrics statements="171" coveredstatements="0" conditionals="74" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="387" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="432" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="436" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="437" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
      </file>
      <file name="conflict-resolver.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\conflict-resolver.js">
        <metrics statements="112" coveredstatements="0" conditionals="90" coveredconditionals="0" methods="27" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="111" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="236" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="393" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="405" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="448" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
      </file>
      <file name="data-exporter.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\data-exporter.js">
        <metrics statements="114" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
      </file>
      <file name="data-migration.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\data-migration.js">
        <metrics statements="147" coveredstatements="0" conditionals="100" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="274" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="373" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
      </file>
      <file name="data-validator.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\data-validator.js">
        <metrics statements="126" coveredstatements="0" conditionals="132" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="128" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="217" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="228" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="264" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="302" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="314" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="324" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="348" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="357" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="368" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="378" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="393" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="402" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="411" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="420" count="0" type="stmt"/>
        <line num="433" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="434" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="444" count="0" type="stmt"/>
        <line num="452" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="454" count="0" type="stmt"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="477" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="478" count="0" type="stmt"/>
        <line num="479" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="492" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
      </file>
      <file name="drag-drop-handler.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\drag-drop-handler.js">
        <metrics statements="143" coveredstatements="0" conditionals="55" coveredconditionals="0" methods="39" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="371" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="423" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
      </file>
      <file name="drag-drop.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\drag-drop.js">
        <metrics statements="163" coveredstatements="0" conditionals="98" coveredconditionals="0" methods="43" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="92" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="345" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="395" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="478" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="479" count="0" type="stmt"/>
      </file>
      <file name="errors.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\errors.js">
        <metrics statements="13" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="3"/>
        <line num="8" count="13" type="stmt"/>
        <line num="9" count="13" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="11" count="13" type="stmt"/>
        <line num="18" count="11" type="stmt"/>
        <line num="19" count="11" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
      </file>
      <file name="filter-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\filter-manager.js">
        <metrics statements="149" coveredstatements="0" conditionals="132" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="204" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="309" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="347" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="362" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="369" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="403" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="409" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="412" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
      </file>
      <file name="operation-queue.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\operation-queue.js">
        <metrics statements="137" coveredstatements="0" conditionals="68" coveredconditionals="0" methods="35" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="363" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="428" count="0" type="stmt"/>
        <line num="436" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
      </file>
      <file name="operation-validator.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\operation-validator.js">
        <metrics statements="158" coveredstatements="0" conditionals="98" coveredconditionals="0" methods="31" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="220" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="395" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="396" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="405" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="415" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="474" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="483" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="484" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="493" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="494" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="505" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="507" count="0" type="stmt"/>
        <line num="520" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="522" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="537" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="538" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
      </file>
      <file name="progress-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\progress-manager.js">
        <metrics statements="128" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="27" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="255" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="311" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="370" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="373" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="393" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="400" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="401" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
      </file>
      <file name="search-cache.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\search-cache.js">
        <metrics statements="122" coveredstatements="0" conditionals="60" coveredconditionals="0" methods="37" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="340" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="393" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="428" count="0" type="stmt"/>
      </file>
      <file name="search-engine.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\search-engine.js">
        <metrics statements="128" coveredstatements="0" conditionals="99" coveredconditionals="0" methods="27" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="363" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
      </file>
      <file name="search-indexer.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\search-indexer.js">
        <metrics statements="215" coveredstatements="0" conditionals="104" coveredconditionals="0" methods="53" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="375" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="386" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="393" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="448" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="456" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="466" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="497" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="511" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="512" count="0" type="stmt"/>
        <line num="522" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="523" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="619" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="650" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="659" count="0" type="stmt"/>
        <line num="660" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="662" count="0" type="stmt"/>
      </file>
      <file name="search-suggester.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\search-suggester.js">
        <metrics statements="135" coveredstatements="0" conditionals="68" coveredconditionals="0" methods="42" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="428" count="0" type="stmt"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="451" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="selection-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\selection-manager.js">
        <metrics statements="142" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="41" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="178" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="422" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="423" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="432" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="444" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
      </file>
      <file name="session-restorer.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\session-restorer.js">
        <metrics statements="106" coveredstatements="0" conditionals="97" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="388" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
      </file>
      <file name="storage-api.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\storage-api.js">
        <metrics statements="44" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
      </file>
      <file name="storage-monitor.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\storage-monitor.js">
        <metrics statements="214" coveredstatements="0" conditionals="94" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="316" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="322" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="331" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="337" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="373" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="471" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="486" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="487" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="514" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="542" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="568" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="570" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="597" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="598" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="615" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="626" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
      </file>
      <file name="storage.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\storage.js">
        <metrics statements="138" coveredstatements="0" conditionals="59" coveredconditionals="0" methods="48" coveredmethods="0"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
      </file>
      <file name="tab-indexer.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\tab-indexer.js">
        <metrics statements="216" coveredstatements="175" conditionals="176" coveredconditionals="113" methods="28" coveredmethods="23"/>
        <line num="9" count="39" type="stmt"/>
        <line num="12" count="39" type="stmt"/>
        <line num="15" count="39" type="stmt"/>
        <line num="18" count="39" type="stmt"/>
        <line num="21" count="39" type="stmt"/>
        <line num="24" count="39" type="stmt"/>
        <line num="25" count="39" type="stmt"/>
        <line num="26" count="39" type="stmt"/>
        <line num="29" count="39" type="stmt"/>
        <line num="38" count="168" type="stmt"/>
        <line num="41" count="168" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="41" type="stmt"/>
        <line num="44" count="168" type="stmt"/>
        <line num="47" count="168" type="cond" truecount="1" falsecount="1"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="168" type="cond" truecount="4" falsecount="0"/>
        <line num="56" count="28" type="stmt"/>
        <line num="57" count="39" type="cond" truecount="2" falsecount="0"/>
        <line num="58" count="29" type="stmt"/>
        <line num="60" count="39" type="stmt"/>
        <line num="65" count="168" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="8" type="stmt"/>
        <line num="70" count="168" type="stmt"/>
        <line num="71" count="168" type="stmt"/>
        <line num="72" count="168" type="stmt"/>
        <line num="75" count="168" type="stmt"/>
        <line num="84" count="9" type="stmt"/>
        <line num="85" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="88" count="9" type="stmt"/>
        <line num="91" count="9" type="stmt"/>
        <line num="92" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="93" count="9" type="stmt"/>
        <line num="94" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="95" count="7" type="stmt"/>
        <line num="100" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="111" count="9" type="cond" truecount="3" falsecount="1"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="124" count="9" type="stmt"/>
        <line num="127" count="9" type="stmt"/>
        <line num="128" count="9" type="stmt"/>
        <line num="129" count="9" type="stmt"/>
        <line num="132" count="9" type="stmt"/>
        <line num="134" count="9" type="stmt"/>
        <line num="144" count="13" type="stmt"/>
        <line num="145" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="148" count="13" type="stmt"/>
        <line num="151" count="13" type="stmt"/>
        <line num="154" count="13" type="cond" truecount="4" falsecount="0"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="160" count="1" type="stmt"/>
        <line num="165" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="172" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="196" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="197" count="2" type="stmt"/>
        <line num="198" count="4" type="stmt"/>
        <line num="199" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="200" count="4" type="stmt"/>
        <line num="201" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="202" count="4" type="stmt"/>
        <line num="209" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="210" count="5" type="stmt"/>
        <line num="211" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="212" count="7" type="stmt"/>
        <line num="214" count="8" type="stmt"/>
        <line num="220" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="221" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="222" count="4" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="229" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="230" count="13" type="stmt"/>
        <line num="231" count="13" type="stmt"/>
        <line num="234" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="13" type="cond" truecount="5" falsecount="0"/>
        <line num="241" count="2" type="stmt"/>
        <line num="242" count="2" type="stmt"/>
        <line num="245" count="13" type="stmt"/>
        <line num="254" count="202" type="cond" truecount="2" falsecount="0"/>
        <line num="263" count="18" type="stmt"/>
        <line num="266" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="267" count="5" type="stmt"/>
        <line num="268" count="5" type="cond" truecount="3" falsecount="1"/>
        <line num="269" count="5" type="stmt"/>
        <line num="273" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="286" count="18" type="cond" truecount="4" falsecount="0"/>
        <line num="287" count="2" type="stmt"/>
        <line num="289" count="2" type="stmt"/>
        <line num="290" count="3" type="stmt"/>
        <line num="291" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="296" count="2" type="stmt"/>
        <line num="299" count="2" type="stmt"/>
        <line num="303" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="2" type="stmt"/>
        <line num="311" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="312" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="313" count="0" type="stmt"/>
        <line num="315" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="323" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="324" count="10" type="stmt"/>
        <line num="328" count="37" type="stmt"/>
        <line num="331" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="332" count="2" type="stmt"/>
        <line num="336" count="18" type="cond" truecount="3" falsecount="1"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="340" count="0" type="stmt"/>
        <line num="345" count="18" type="cond" truecount="4" falsecount="0"/>
        <line num="348" count="18" type="cond" truecount="4" falsecount="0"/>
        <line num="349" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="350" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="351" count="2" type="stmt"/>
        <line num="354" count="18" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="377" count="2" type="stmt"/>
        <line num="394" count="517" type="stmt"/>
        <line num="397" count="517" type="stmt"/>
        <line num="398" count="517" type="stmt"/>
        <line num="400" count="517" type="stmt"/>
        <line num="401" count="1591" type="stmt"/>
        <line num="402" count="1591" type="cond" truecount="1" falsecount="1"/>
        <line num="403" count="1591" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="409" count="517" type="stmt"/>
        <line num="417" count="43" type="stmt"/>
        <line num="418" count="40" type="cond" truecount="1" falsecount="1"/>
        <line num="419" count="40" type="stmt"/>
        <line num="428" count="181" type="stmt"/>
        <line num="429" count="181" type="stmt"/>
        <line num="431" count="181" type="stmt"/>
        <line num="432" count="366" type="cond" truecount="2" falsecount="0"/>
        <line num="433" count="343" type="cond" truecount="2" falsecount="0"/>
        <line num="434" count="185" type="stmt"/>
        <line num="436" count="343" type="stmt"/>
        <line num="437" count="23" type="cond" truecount="1" falsecount="1"/>
        <line num="438" count="23" type="stmt"/>
        <line num="439" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="440" count="14" type="stmt"/>
        <line num="441" count="14" type="cond" truecount="1" falsecount="1"/>
        <line num="442" count="14" type="stmt"/>
        <line num="455" count="183" type="stmt"/>
        <line num="456" count="573" type="stmt"/>
        <line num="457" count="368" type="stmt"/>
        <line num="458" count="368" type="stmt"/>
        <line num="466" count="2" type="stmt"/>
        <line num="467" count="2" type="stmt"/>
        <line num="470" count="2" type="stmt"/>
        <line num="472" count="2" type="stmt"/>
        <line num="473" count="6" type="stmt"/>
        <line num="474" count="6" type="stmt"/>
        <line num="477" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="478" count="3" type="stmt"/>
        <line num="482" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="483" count="6" type="stmt"/>
        <line num="484" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="485" count="1" type="stmt"/>
        <line num="490" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="491" count="3" type="stmt"/>
        <line num="496" count="2" type="stmt"/>
        <line num="497" count="6" type="stmt"/>
        <line num="498" count="1" type="stmt"/>
        <line num="506" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="508" count="18" type="stmt"/>
        <line num="509" count="20" type="stmt"/>
        <line num="510" count="20" type="stmt"/>
        <line num="512" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="513" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="514" count="14" type="stmt"/>
        <line num="519" count="1" type="stmt"/>
      </file>
      <file name="tab-opener.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\tab-opener.js">
        <metrics statements="84" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
      </file>
      <file name="tab-updater.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\tab-updater.js">
        <metrics statements="103" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
      </file>
      <file name="tab-validator.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\tab-validator.js">
        <metrics statements="94" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
      </file>
      <file name="undo-manager.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\undo-manager.js">
        <metrics statements="193" coveredstatements="0" conditionals="101" coveredconditionals="0" methods="55" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="400" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="401" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="446" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="456" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="472" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="490" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="491" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="533" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="556" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="569" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="570" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="579" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="580" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="583" count="0" type="stmt"/>
        <line num="591" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
      </file>
      <file name="uuid.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\uuid.js">
        <metrics statements="15" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
      </file>
      <file name="virtual-scroll.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\virtual-scroll.js">
        <metrics statements="133" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="26" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="370" count="0" type="stmt"/>
      </file>
      <file name="window-organizer.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\utils\window-organizer.js">
        <metrics statements="148" coveredstatements="0" conditionals="105" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="160" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="272" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="291" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="300" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.validators">
      <metrics statements="74" coveredstatements="61" conditionals="89" coveredconditionals="66" methods="22" coveredmethods="21"/>
      <file name="tab-validator.js" path="D:\PythonProjects\__mytest\test_cc-mytab3\src\validators\tab-validator.js">
        <metrics statements="74" coveredstatements="61" conditionals="89" coveredconditionals="66" methods="22" coveredmethods="21"/>
        <line num="6" count="1" type="stmt"/>
        <line num="14" count="500" type="stmt"/>
        <line num="15" count="500" type="stmt"/>
        <line num="17" count="498" type="stmt"/>
        <line num="18" count="498" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="30" count="181" type="stmt"/>
        <line num="31" count="181" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="181" type="stmt"/>
        <line num="37" count="499" type="cond" truecount="2" falsecount="0"/>
        <line num="38" count="500" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="235" type="stmt"/>
        <line num="40" count="181" type="stmt"/>
        <line num="41" count="507" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="181" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="181" type="cond" truecount="2" falsecount="0"/>
        <line num="44" count="181" type="cond" truecount="2" falsecount="0"/>
        <line num="45" count="245" type="cond" truecount="2" falsecount="0"/>
        <line num="46" count="182" type="stmt"/>
        <line num="47" count="204" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="52" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="68" count="501" type="stmt"/>
        <line num="69" count="3131" type="cond" truecount="4" falsecount="0"/>
        <line num="70" count="1" type="stmt"/>
        <line num="81" count="520" type="stmt"/>
        <line num="82" count="3458" type="cond" truecount="4" falsecount="0"/>
        <line num="83" count="181" type="stmt"/>
        <line num="86" count="3277" type="stmt"/>
        <line num="87" count="3277" type="cond" truecount="4" falsecount="0"/>
        <line num="88" count="4" type="stmt"/>
        <line num="100" count="320" type="stmt"/>
        <line num="103" count="319" type="stmt"/>
        <line num="106" count="315" type="cond" truecount="4" falsecount="0"/>
        <line num="107" count="1" type="stmt"/>
        <line num="110" count="314" type="cond" truecount="4" falsecount="0"/>
        <line num="111" count="1" type="stmt"/>
        <line num="114" count="313" type="cond" truecount="4" falsecount="0"/>
        <line num="115" count="1" type="stmt"/>
        <line num="126" count="181" type="stmt"/>
        <line num="129" count="181" type="stmt"/>
        <line num="132" count="181" type="cond" truecount="1" falsecount="1"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="181" type="cond" truecount="1" falsecount="1"/>
        <line num="137" count="0" type="stmt"/>
        <line num="148" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="149" count="1" type="stmt"/>
        <line num="153" count="20" type="cond" truecount="1" falsecount="1"/>
        <line num="154" count="0" type="stmt"/>
        <line num="158" count="20" type="stmt"/>
        <line num="167" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="168" count="2" type="cond" truecount="4" falsecount="1"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="174" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="180" count="4" type="stmt"/>
        <line num="181" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="187" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="188" count="0" type="stmt"/>
        <line num="192" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="194" count="0" type="stmt"/>
        <line num="198" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="18" type="cond" truecount="2" falsecount="3"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
