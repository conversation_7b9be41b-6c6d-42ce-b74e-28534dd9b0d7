// Background service worker entry point
import { <PERSON><PERSON>and<PERSON> } from './core/message-handler.js';
import { StateManager } from './core/state-manager.js';
import { globalEventBus, Events } from './core/event-bus.js';
import { ErrorHandler } from './core/error-handler.js';
import { PerformanceMonitor } from './core/performance-monitor.js';

// Initialize core services
const messageHandler = new MessageHandler();
const stateManager = new StateManager();
const errorHandler = new ErrorHandler();
const performanceMonitor = new PerformanceMonitor();

// Export instances for use in other modules
export { messageHandler, stateManager, errorHandler, performanceMonitor };

// Initialize extension on install
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('Extension installed:', details);
  
  try {
    // Initialize state
    await stateManager.restore();
    
    // Initialize message handler
    messageHandler.initialize();
    
    // Emit installation event
    if (details.reason === 'install') {
      globalEventBus.emit(Events.EXTENSION_INSTALLED, details);
    } else if (details.reason === 'update') {
      globalEventBus.emit(Events.EXTENSION_UPDATED, details);
    }
    
    // Start performance monitoring
    performanceMonitor.setupMonitoring();
    
    console.log('Extension initialization complete');
  } catch (error) {
    errorHandler.handle(error, { context: 'initialization' });
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(async () => {
  console.log('Extension started');
  
  try {
    // Restore state
    await stateManager.restore();
    
    // Initialize message handler
    messageHandler.initialize();
    
    // Start monitoring
    performanceMonitor.setupMonitoring();
  } catch (error) {
    errorHandler.handle(error, { context: 'startup' });
  }
});

// Handle suspend event
chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension suspending');
  
  // Persist state before suspension
  stateManager.persist().catch(error => {
    console.error('Failed to persist state:', error);
  });
});

// Set up Chrome API event listeners for state synchronization
function setupChromeListeners() {
  // Tab events
  chrome.tabs.onCreated.addListener(tab => {
    performanceMonitor.startTimer('tabCreated');
    stateManager.handleTabCreated(tab);
    globalEventBus.emit(Events.TAB_CREATED, tab);
    performanceMonitor.endTimer('tabCreated');
  });
  
  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete') {
      stateManager.handleTabUpdated(tabId, changeInfo, tab);
      globalEventBus.emit(Events.TAB_UPDATED, { tabId, changeInfo, tab });
    }
  });
  
  chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    stateManager.handleTabRemoved(tabId, removeInfo);
    globalEventBus.emit(Events.TAB_REMOVED, { tabId, removeInfo });
  });
  
  chrome.tabs.onActivated.addListener(activeInfo => {
    stateManager.handleTabActivated(activeInfo);
    globalEventBus.emit(Events.TAB_ACTIVATED, activeInfo);
  });
  
  chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
    stateManager.handleTabMoved(tabId, moveInfo);
    globalEventBus.emit(Events.TAB_MOVED, { tabId, moveInfo });
  });
  
  // Window events
  chrome.windows.onCreated.addListener(window => {
    stateManager.handleWindowCreated(window);
    globalEventBus.emit(Events.WINDOW_CREATED, window);
  });
  
  chrome.windows.onRemoved.addListener(windowId => {
    stateManager.handleWindowRemoved(windowId);
    globalEventBus.emit(Events.WINDOW_REMOVED, windowId);
  });
  
  chrome.windows.onFocusChanged.addListener(windowId => {
    stateManager.handleWindowFocusChanged(windowId);
    globalEventBus.emit(Events.WINDOW_FOCUSED, windowId);
  });
  
  // Storage events
  chrome.storage.onChanged.addListener((changes, areaName) => {
    globalEventBus.emit(Events.STORAGE_CHANGED, { changes, areaName });
  });
}

// Initialize Chrome listeners
setupChromeListeners();

// Handle extension icon click to open management page
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // Open the management page in a new tab
    await chrome.tabs.create({
      url: chrome.runtime.getURL('src/pages/management.html'),
      active: true
    });
  } catch (error) {
    errorHandler.handle(error, { context: 'action_click' });
  }
});

// Keep service worker alive
const keepAlive = () => {
  chrome.runtime.getPlatformInfo(() => {
    // Just calling an API to keep the service worker alive
  });
};

// Keep alive every 25 seconds
setInterval(keepAlive, 25000);

console.log('Background service worker loaded');