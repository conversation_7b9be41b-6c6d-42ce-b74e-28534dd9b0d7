<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1a73e8;
            border-bottom: 2px solid #e8f0fe;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .test-pass { background: #1e8e3e; }
        .test-fail { background: #d93025; }
        .test-pending { background: #f9ab00; }
        .test-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1557b0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e8f0fe;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            background: #fce8e6;
            color: #d93025;
        }
        .success {
            background: #e6f4ea;
            color: #1e8e3e;
        }
    </style>
</head>
<body>
    <h1>修复验证测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">🔧 修复验证</h2>
        
        <div class="test-item">
            <div class="test-status test-pending" id="status-1">?</div>
            <span>测试1: UUID模块导入</span>
            <button class="test-button" onclick="testUUIDImport()">测试</button>
        </div>
        <div id="result-1" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-2">?</div>
            <span>测试2: 窗口API导入</span>
            <button class="test-button" onclick="testWindowAPI()">测试</button>
        </div>
        <div id="result-2" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-3">?</div>
            <span>测试3: 标签页API导入</span>
            <button class="test-button" onclick="testTabAPI()">测试</button>
        </div>
        <div id="result-3" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-4">?</div>
            <span>测试4: 消息处理器</span>
            <button class="test-button" onclick="testMessageHandler()">测试</button>
        </div>
        <div id="result-4" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-5">?</div>
            <span>测试5: 浏览器数据导入</span>
            <button class="test-button" onclick="testBrowserImport()">测试</button>
        </div>
        <div id="result-5" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <button class="test-button" onclick="runAllTests()" style="font-size: 16px; padding: 12px 24px;">
            🚀 运行所有测试
        </button>
        <button class="test-button" onclick="openMainPage()" style="font-size: 16px; padding: 12px 24px;">
            📱 打开主页面
        </button>
    </div>

    <script type="module">
        // 设置测试结果
        function setTestResult(testId, passed, message) {
            const statusEl = document.getElementById(`status-${testId}`);
            const resultEl = document.getElementById(`result-${testId}`);
            
            if (passed) {
                statusEl.className = 'test-status test-pass';
                statusEl.textContent = '✓';
                resultEl.className = 'test-result success';
            } else {
                statusEl.className = 'test-status test-fail';
                statusEl.textContent = '✗';
                resultEl.className = 'test-result error';
            }
            
            resultEl.textContent = message;
            resultEl.style.display = 'block';
        }

        // 测试UUID模块导入
        window.testUUIDImport = async function() {
            try {
                const { v4 } = await import('/src/utils/uuid.js');
                const uuid = v4();
                if (uuid && uuid.length === 36 && uuid.includes('-')) {
                    setTestResult(1, true, `✅ UUID模块导入成功\n生成的UUID: ${uuid}`);
                } else {
                    setTestResult(1, false, `❌ UUID格式不正确: ${uuid}`);
                }
            } catch (error) {
                setTestResult(1, false, `❌ UUID模块导入失败: ${error.message}`);
            }
        };

        // 测试窗口API导入
        window.testWindowAPI = async function() {
            try {
                const { getAllWindows } = await import('/src/api/window-api.js');
                const windows = await getAllWindows();
                setTestResult(2, true, `✅ 窗口API导入成功\n当前窗口数量: ${windows.length}`);
            } catch (error) {
                setTestResult(2, false, `❌ 窗口API导入失败: ${error.message}`);
            }
        };

        // 测试标签页API导入
        window.testTabAPI = async function() {
            try {
                const { getAllTabs } = await import('/src/api/tab-api.js');
                const tabs = await getAllTabs();
                setTestResult(3, true, `✅ 标签页API导入成功\n当前标签页数量: ${tabs.length}`);
            } catch (error) {
                setTestResult(3, false, `❌ 标签页API导入失败: ${error.message}`);
            }
        };

        // 测试消息处理器
        window.testMessageHandler = async function() {
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    // 测试发送消息
                    const response = await chrome.runtime.sendMessage({
                        type: 'GET_BROWSER_DATA',
                        payload: { includeIncognito: false }
                    });
                    
                    if (response && response.windows) {
                        setTestResult(4, true, `✅ 消息处理器工作正常\n获取到 ${response.windows.length} 个窗口`);
                    } else {
                        setTestResult(4, false, `❌ 消息处理器响应异常: ${JSON.stringify(response)}`);
                    }
                } else {
                    setTestResult(4, false, '❌ Chrome扩展环境不可用');
                }
            } catch (error) {
                setTestResult(4, false, `❌ 消息处理器测试失败: ${error.message}`);
            }
        };

        // 测试浏览器数据导入
        window.testBrowserImport = async function() {
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await chrome.runtime.sendMessage({
                        type: 'GET_BROWSER_DATA',
                        payload: { includeIncognito: false }
                    });
                    
                    if (response && response.windows && response.windows.length > 0) {
                        const totalTabs = response.windows.reduce((sum, win) => sum + (win.tabs ? win.tabs.length : 0), 0);
                        setTestResult(5, true, `✅ 浏览器数据导入功能正常\n窗口数: ${response.windows.length}\n标签页数: ${totalTabs}`);
                    } else {
                        setTestResult(5, false, '❌ 未获取到浏览器数据');
                    }
                } else {
                    setTestResult(5, false, '❌ Chrome扩展环境不可用');
                }
            } catch (error) {
                setTestResult(5, false, `❌ 浏览器数据导入测试失败: ${error.message}`);
            }
        };

        // 运行所有测试
        window.runAllTests = async function() {
            const tests = [
                window.testUUIDImport,
                window.testWindowAPI,
                window.testTabAPI,
                window.testMessageHandler,
                window.testBrowserImport
            ];

            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                // 添加小延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        };

        // 打开主页面
        window.openMainPage = function() {
            window.open('/src/pages/management.html', '_blank');
        };
    </script>
</body>
</html>
