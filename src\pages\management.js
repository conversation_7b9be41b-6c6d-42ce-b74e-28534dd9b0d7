/**
 * MyTab3 主应用逻辑
 */

// 导入API模块
import { getAllWindows, createWindow } from '../api/window-api.js';
import { getAllTabs, createTab } from '../api/tab-api.js';
class MyTab3App {
  constructor() {
    // 组件实例
    this.components = {
      windowList: null,
      toolbar: null,
      settings: null,
      dragDropManager: null,
      virtualScroller: null
    };
    
    // 应用状态
    this.state = {
      windows: [],
      tabs: [],
      filteredTabs: [],
      viewMode: 'card',
      searchQuery: '',
      selectedTabs: new Set(),
      selectedWindowId: null
    };
    
    // 交互管理
    this.interactionManager = null;
    this.keyboardShortcuts = null;
    
    // 性能优化
    this.lazyImageLoader = null;
    this.updateDebounced = this.debounce(this.updateDisplay.bind(this), 100);
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      // 初始化组件
      this.initComponents();
      
      // 绑定事件
      this.bindEvents();
      
      // 加载数据
      await this.loadData();
      
      // 渲染界面
      this.render();
      
      // 初始化交互
      this.initInteractions();
      
      console.log('MyTab3 初始化完成');
    } catch (error) {
      console.error('初始化失败:', error);
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  /**
   * 初始化组件
   */
  initComponents() {
    // 窗口列表
    this.components.windowList = new WindowList(
      document.querySelector('.window-list-container')
    );
    this.components.windowList.onWindowSelect = (windowId) => {
      this.handleWindowSelect(windowId);
    };
    
    // 工具栏
    this.components.toolbar = new Toolbar(
      document.querySelector('.toolbar')
    );
    this.components.toolbar.onSearch = (query) => {
      this.handleSearch(query);
    };
    this.components.toolbar.onViewChange = (viewMode) => {
      this.handleViewChange(viewMode);
    };
    this.components.toolbar.onImportBrowser = (data) => {
      this.handleImport(data);
    };
    this.components.toolbar.onImport = (data) => {
      this.handleImport(data);
    };
    this.components.toolbar.onExport = () => {
      return this.handleExport();
    };
    this.components.toolbar.onSettings = () => {
      this.components.settings.show();
    };
    
    // 设置面板
    this.components.settings = new SettingsPanel();
    this.components.settings.onSettingsChange = (settings) => {
      this.handleSettingsChange(settings);
    };
    
    // 拖拽管理器
    this.components.dragDropManager = new DragDropManager();
    this.components.dragDropManager.on('drop', (source, target, sourceData, targetData) => {
      this.handleDrop(source, target, sourceData, targetData);
    });
    
    // 懒加载图片
    this.lazyImageLoader = new LazyImageLoader();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // Chrome API 事件
    chrome.tabs.onCreated.addListener(this.handleTabCreated.bind(this));
    chrome.tabs.onRemoved.addListener(this.handleTabRemoved.bind(this));
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
    chrome.tabs.onMoved.addListener(this.handleTabMoved.bind(this));
    chrome.windows.onCreated.addListener(this.handleWindowCreated.bind(this));
    chrome.windows.onRemoved.addListener(this.handleWindowRemoved.bind(this));
    
    // 自定义事件
    document.addEventListener('search', (e) => {
      this.handleSearch(e.detail.query);
    });
    
    document.addEventListener('viewChange', (e) => {
      this.handleViewChange(e.detail.viewMode);
    });
    
    document.addEventListener('settingsChanged', (e) => {
      this.handleSettingsChange(e.detail);
    });
    
    // 窗口大小变化
    window.addEventListener('resize', this.updateDebounced);
  }

  /**
   * 初始化交互功能
   */
  initInteractions() {
    // 交互管理器
    this.interactionManager = new InteractionManager();
    this.interactionManager.init();
    
    // 键盘快捷键
    this.keyboardShortcuts = new KeyboardShortcuts();
    this.keyboardShortcuts.init();
    
    // 标签卡片事件委托
    document.querySelector('.tabs-container').addEventListener('click', (e) => {
      this.handleTabContainerClick(e);
    });
    
    // 上下文菜单
    document.addEventListener('contextmenu', (e) => {
      const tabElement = e.target.closest('.tab-card, .tab-list-item, .tab-compact-item');
      if (tabElement) {
        e.preventDefault();
        this.showContextMenu(e.pageX, e.pageY, tabElement);
      }
    });
  }

  /**
   * 加载数据
   */
  async loadData() {
    try {
      // 从本地存储加载窗口和标签数据（书签式管理）
      // 获取所有窗口数据
      this.state.windows = await getAllWindows();

      // 获取所有标签页数据
      this.state.tabs = await getAllTabs();

      // 初始过滤
      this.filterTabs();

      console.log('数据加载完成:', {
        windows: this.state.windows.length,
        tabs: this.state.tabs.length
      });

    } catch (error) {
      console.error('加载数据失败:', error);
      // 如果没有数据，初始化空状态
      this.state.windows = [];
      this.state.tabs = [];
      this.filterTabs();
    }
  }

  /**
   * 渲染界面
   */
  render() {
    // 渲染窗口列表
    this.components.windowList.setWindows(this.state.windows);
    
    // 渲染标签页
    this.renderTabs();
    
    // 更新状态栏
    this.updateStatusBar();
  }

  /**
   * 渲染标签页
   */
  renderTabs() {
    const container = document.querySelector('.tabs-container');
    const settings = this.components.settings.getSettings();
    
    // 清空容器
    container.innerHTML = '';
    
    // 设置视图类
    container.className = `tabs-container view-${this.state.viewMode}`;
    
    // 如果没有标签
    if (this.state.filteredTabs.length === 0) {
      container.innerHTML = this.renderEmptyState();
      return;
    }
    
    // 使用虚拟滚动或普通渲染
    if (settings.virtualScroll && this.state.filteredTabs.length > 100) {
      this.renderWithVirtualScroll();
    } else {
      this.renderNormalTabs();
    }
  }

  /**
   * 普通渲染标签页
   */
  renderNormalTabs() {
    const container = document.querySelector('.tabs-container');
    const fragment = document.createDocumentFragment();
    
    this.state.filteredTabs.forEach(tab => {
      const tabCard = new TabCard(tab, this.state.viewMode);
      tabCard.setSelected(this.state.selectedTabs.has(tab.id));
      
      const element = tabCard.createElement();
      this.components.dragDropManager.makeDraggable(element, {
        sortable: true,
        usePlaceholder: true
      });
      
      fragment.appendChild(element);
    });
    
    container.appendChild(fragment);
    
    // 懒加载图片
    container.querySelectorAll('img[data-src]').forEach(img => {
      this.lazyImageLoader.observe(img);
    });
  }

  /**
   * 使用虚拟滚动渲染
   */
  renderWithVirtualScroll() {
    const container = document.querySelector('.tabs-container');
    
    // 销毁现有的虚拟滚动器
    if (this.components.virtualScroller) {
      this.components.virtualScroller.destroy();
    }
    
    // 创建新的虚拟滚动器
    this.components.virtualScroller = new VirtualScroller({
      container: container,
      items: this.state.filteredTabs,
      itemHeight: this.getItemHeight(),
      renderItem: (tab) => {
        const tabCard = new TabCard(tab, this.state.viewMode);
        tabCard.setSelected(this.state.selectedTabs.has(tab.id));
        return tabCard.render();
      }
    });
  }

  /**
   * 获取项目高度
   */
  getItemHeight() {
    switch(this.state.viewMode) {
      case 'card': return 200;
      case 'list': return 60;
      case 'compact': return 40;
      default: return 80;
    }
  }

  /**
   * 渲染空状态
   */
  renderEmptyState() {
    return `
      <div class="empty-state">
        <div class="empty-state-icon">📭</div>
        <div class="empty-state-title">没有找到标签页</div>
        <div class="empty-state-description">
          ${this.state.searchQuery ? 
            '尝试使用其他关键词搜索' : 
            '当前没有打开的标签页'}
        </div>
      </div>
    `;
  }

  /**
   * 处理窗口选择
   */
  handleWindowSelect(windowId) {
    this.state.selectedWindowId = windowId;
    this.filterTabs();
    this.renderTabs();
  }

  /**
   * 处理搜索
   */
  handleSearch(query) {
    this.state.searchQuery = query;
    this.filterTabs();
    this.renderTabs();
    
    // 同时搜索窗口
    this.components.windowList.search(query);
  }

  /**
   * 处理视图切换
   */
  handleViewChange(viewMode) {
    this.state.viewMode = viewMode;
    this.renderTabs();
  }

  /**
   * 处理导入
   */
  async handleImport(data) {
    try {
      if (!data || !data.windows) {
        throw new Error('无效的导入数据');
      }

      // 使用已导入的函数

      // 导入窗口和标签到本地存储（书签式管理）
      for (const windowData of data.windows) {
        // 创建窗口记录
        const newWindow = await createWindow({
          name: windowData.name || `窗口 ${windowData.id}`,
          color: windowData.color || '#2563eb',
          metadata: {
            originalId: windowData.id,
            importedAt: Date.now()
          }
        });

        // 创建标签记录
        if (windowData.tabs && windowData.tabs.length > 0) {
          for (const tabData of windowData.tabs) {
            await createTab({
              windowId: newWindow.windowId,
              url: tabData.url,
              title: tabData.title,
              favIconUrl: tabData.favIconUrl,
              metadata: {
                originalId: tabData.id,
                importedAt: Date.now()
              }
            });
          }
        }
      }

      // 重新加载数据
      await this.loadData();
      this.render();

      this.showSuccess('会话导入成功');
    } catch (error) {
      console.error('导入失败:', error);
      this.showError('导入失败：' + error.message);
    }
  }

  /**
   * 处理导出
   */
  handleExport() {
    const exportData = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      windows: this.state.windows.map(window => ({
        id: window.id,
        state: window.state,
        customName: window.customName,
        tabs: this.state.tabs
          .filter(tab => tab.windowId === window.id)
          .map(tab => ({
            title: tab.title,
            url: tab.url,
            active: tab.active,
            pinned: tab.pinned
          }))
      }))
    };
    
    return exportData;
  }

  /**
   * 处理设置变更
   */
  handleSettingsChange(settings) {
    // 应用新设置
    if (settings.theme !== this.currentTheme) {
      this.applyTheme(settings.theme);
    }
    
    // 重新渲染（如果需要）
    if (settings.showFavicons !== this.previousSettings?.showFavicons ||
        settings.showUrls !== this.previousSettings?.showUrls) {
      this.renderTabs();
    }
    
    this.previousSettings = settings;
  }

  /**
   * 处理标签容器点击
   */
  handleTabContainerClick(e) {
    const action = e.target.dataset.action;
    if (!action) return;
    
    const tabElement = e.target.closest('[data-tab-id]');
    if (!tabElement) return;
    
    const tabId = parseInt(tabElement.dataset.tabId);
    TabCard.handleAction(action, tabId);
  }

  /**
   * 处理拖放
   */
  handleDrop(source, target, sourceData, targetData) {
    if (targetData.container) {
      // 拖放到窗口
      const tabId = parseInt(sourceData.tabId);
      const windowId = parseInt(target.dataset.windowId);
      
      if (tabId && windowId) {
        chrome.tabs.move(tabId, { windowId, index: -1 });
      }
    } else {
      // 重新排序
      this.updateTabOrder();
    }
  }

  /**
   * 过滤标签
   */
  filterTabs() {
    let filtered = [...this.state.tabs];
    
    // 按窗口过滤
    if (this.state.selectedWindowId !== null) {
      filtered = filtered.filter(tab => tab.windowId === this.state.selectedWindowId);
    }
    
    // 按搜索查询过滤
    if (this.state.searchQuery) {
      filtered = filtered.filter(tab => TabCard.matchesSearch(tab, this.state.searchQuery));
    }
    
    this.state.filteredTabs = filtered;
  }

  /**
   * 更新状态栏
   */
  updateStatusBar() {
    const tabCount = document.querySelector('.tab-count');
    const windowCount = document.querySelector('.window-count');
    const lastSync = document.querySelector('.last-sync');
    
    if (tabCount) {
      tabCount.textContent = `共 ${this.state.tabs.length} 个标签页`;
    }
    
    if (windowCount) {
      windowCount.textContent = `${this.state.windows.length} 个窗口`;
    }
    
    if (lastSync) {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      lastSync.textContent = `最后同步: ${time}`;
    }
  }

  /**
   * Chrome事件处理器
   */
  handleTabCreated(tab) {
    this.loadData().then(() => this.render());
  }

  handleTabRemoved(tabId, removeInfo) {
    this.state.tabs = this.state.tabs.filter(tab => tab.id !== tabId);
    this.state.selectedTabs.delete(tabId);
    this.filterTabs();
    this.renderTabs();
    this.updateStatusBar();
  }

  handleTabUpdated(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete') {
      this.loadData().then(() => this.render());
    }
  }

  handleTabMoved(tabId, moveInfo) {
    this.loadData().then(() => this.render());
  }

  handleWindowCreated(window) {
    this.loadData().then(() => this.render());
  }

  handleWindowRemoved(windowId) {
    this.state.windows = this.state.windows.filter(w => w.id !== windowId);
    this.state.tabs = this.state.tabs.filter(tab => tab.windowId !== windowId);
    this.filterTabs();
    this.render();
  }

  /**
   * 工具方法
   */
  getWindowCustomName(windowId) {
    return localStorage.getItem(`window_name_${windowId}`) || '';
  }

  getWindowCollapsedState(windowId) {
    return localStorage.getItem(`window_collapsed_${windowId}`) === 'true';
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
  }

  showSuccess(message) {
    this.showToast(message, 'success');
  }

  showError(message) {
    this.showToast(message, 'error');
  }

  showToast(message, type = 'info') {
    TabCard.showToast(message);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  updateDisplay() {
    this.renderTabs();
  }
}

/**
 * 交互管理器
 */
class InteractionManager {
  constructor() {
    this.selectedTabs = new Set();
    this.lastClickedTab = null;
  }

  init() {
    // 代理实现见文档
  }
}

/**
 * 键盘快捷键管理器
 */
class KeyboardShortcuts {
  constructor() {
    this.shortcuts = {
      'Ctrl+A': () => this.selectAll(),
      'Ctrl+D': () => this.deselectAll(),
      'Delete': () => this.closeSelected(),
      'Ctrl+F': () => this.focusSearch(),
      'Escape': () => this.cancelOperation()
    };
  }

  init() {
    document.addEventListener('keydown', (e) => {
      const key = this.getKeyCombo(e);
      const handler = this.shortcuts[key];
      
      if (handler) {
        e.preventDefault();
        handler();
      }
    });
  }

  getKeyCombo(e) {
    const keys = [];
    if (e.ctrlKey) keys.push('Ctrl');
    if (e.shiftKey) keys.push('Shift');
    if (e.altKey) keys.push('Alt');
    if (e.metaKey) keys.push('Cmd');
    
    if (e.key && e.key !== 'Control' && e.key !== 'Shift' && e.key !== 'Alt' && e.key !== 'Meta') {
      keys.push(e.key.length === 1 ? e.key.toUpperCase() : e.key);
    }
    
    return keys.join('+');
  }

  selectAll() {
    document.querySelectorAll('.tab-select').forEach(checkbox => {
      checkbox.checked = true;
    });
  }

  deselectAll() {
    document.querySelectorAll('.tab-select').forEach(checkbox => {
      checkbox.checked = false;
    });
  }

  closeSelected() {
    const selected = Array.from(document.querySelectorAll('.tab-select:checked'))
      .map(checkbox => parseInt(checkbox.dataset.tabId));
    
    if (selected.length > 0 && confirm(`确定要关闭 ${selected.length} 个标签页吗？`)) {
      chrome.tabs.remove(selected);
    }
  }

  focusSearch() {
    const searchBox = document.querySelector('.search-box');
    if (searchBox) {
      searchBox.focus();
      searchBox.select();
    }
  }

  cancelOperation() {
    // 取消当前操作
  }
}

/**
 * 懒加载图片管理器
 */
class LazyImageLoader {
  constructor() {
    this.imageObserver = null;
    this.init();
  }

  init() {
    this.imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          this.loadImage(img);
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px'
    });
  }

  observe(img) {
    if ('IntersectionObserver' in window) {
      this.imageObserver.observe(img);
    } else {
      this.loadImage(img);
    }
  }

  loadImage(img) {
    const src = img.dataset.src;
    if (src) {
      img.src = src;
      img.onload = () => {
        img.classList.add('loaded');
      };
      img.onerror = () => {
        img.src = '../assets/default-icon.png';
      };
    }
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  const app = new MyTab3App();
  app.init();
});