# 数据持久化模块需求文档

## 1. 模块概述

### 1.1 核心功能
数据持久化模块负责管理扩展的所有数据存储需求，包括窗口信息、标签页数据、用户设置等。通过封装chrome.storage.local API，提供统一、高效、可靠的数据存储服务。

### 1.2 技术选型
- **存储API**: chrome.storage.local
- **存储限制**: 5MB
- **数据格式**: JSON
- **访问方式**: 异步Promise接口

### 1.3 数据存储策略
- **实时保存**: 关键数据变更立即持久化
- **批量更新**: 非关键数据批量写入，减少I/O
- **增量更新**: 只更新变化的部分，避免全量写入
- **自动清理**: 定期清理过期数据，优化存储空间

## 2. 存储结构设计

### 2.1 数据模型
```javascript
{
  // 窗口数据集合
  "windows": {
    "windowId1": {
      id: "windowId1",
      name: "工作窗口",
      color: "#4285f4",
      icon: "work",
      tabIds: ["tabId1", "tabId2"],
      createdAt: 1234567890,
      updatedAt: 1234567890,
      metadata: {
        position: { top: 100, left: 100 },
        size: { width: 1200, height: 800 },
        state: "normal" // normal, minimized, maximized
      }
    }
  },
  
  // 标签页数据集合
  "tabs": {
    "tabId1": {
      id: "tabId1",
      windowId: "windowId1",
      url: "https://example.com",
      title: "Example Page",
      favIconUrl: "https://example.com/favicon.ico",
      pinned: false,
      active: true,
      index: 0,
      createdAt: 1234567890,
      updatedAt: 1234567890,
      metadata: {
        scrollPosition: 0,
        zoomLevel: 1.0,
        customNote: "Important reference"
      }
    }
  },
  
  // 用户设置
  "settings": {
    theme: "light", // light, dark, system
    viewMode: "card", // card, list, compact
    sortBy: "recent", // recent, alphabetical, custom
    autoSave: true,
    autoSaveInterval: 30000, // 毫秒
    showFavicons: true,
    confirmBeforeDelete: true,
    shortcuts: {
      saveWindow: "Ctrl+S",
      quickSwitch: "Ctrl+Tab"
    }
  },
  
  // 元数据信息
  "metadata": {
    version: "1.0.0",
    lastUpdated: 1234567890,
    totalTabs: 42,
    totalWindows: 5,
    storageUsed: 1048576, // bytes
    lastBackup: 1234567890,
    installDate: 1234567890
  }
}
```

### 2.2 数据关系
- 窗口与标签页: 一对多关系，通过windowId关联
- 数据完整性: 删除窗口时自动清理相关标签页
- 引用完整性: 标签页必须属于有效窗口

## 3. 存储API封装

### 3.1 核心API接口
```javascript
class StorageManager {
  /**
   * 保存数据
   * @param {string} key - 存储键名
   * @param {any} data - 要存储的数据
   * @returns {Promise<void>}
   */
  async save(key, data) {
    // 数据验证
    // 缓存更新
    // 持久化存储
    // 错误处理
  }
  
  /**
   * 加载数据
   * @param {string} key - 存储键名
   * @returns {Promise<any>} - 存储的数据
   */
  async load(key) {
    // 缓存检查
    // 存储读取
    // 数据验证
    // 错误处理
  }
  
  /**
   * 删除数据
   * @param {string} key - 存储键名
   * @returns {Promise<void>}
   */
  async remove(key) {
    // 缓存清除
    // 存储删除
    // 关联数据处理
  }
  
  /**
   * 清空所有数据
   * @returns {Promise<void>}
   */
  async clear() {
    // 确认机制
    // 缓存清空
    // 存储清空
    // 重置元数据
  }
  
  /**
   * 获取已使用的存储空间
   * @returns {Promise<number>} - 字节数
   */
  async getBytesInUse() {
    // 获取存储大小
    // 缓存计算
    // 返回总大小
  }
}
```

### 3.2 扩展API接口
```javascript
// 批量操作
async batchSave(items) { }
async batchLoad(keys) { }
async batchRemove(keys) { }

// 条件查询
async query(filter) { }
async count(filter) { }
async exists(key) { }

// 事务操作
async transaction(operations) { }
async rollback(transactionId) { }
```

## 4. 数据操作优化

### 4.1 批量读写优化
```javascript
// 批量写入示例
const batchWrite = async (updates) => {
  const batch = {};
  
  // 合并更新
  for (const [key, value] of Object.entries(updates)) {
    batch[key] = value;
  }
  
  // 单次写入
  await chrome.storage.local.set(batch);
  
  // 更新缓存
  updateCache(batch);
};

// 批量读取示例
const batchRead = async (keys) => {
  // 缓存优先
  const cached = getCachedValues(keys);
  const uncachedKeys = keys.filter(key => !cached[key]);
  
  // 读取未缓存数据
  if (uncachedKeys.length > 0) {
    const stored = await chrome.storage.local.get(uncachedKeys);
    updateCache(stored);
    return { ...cached, ...stored };
  }
  
  return cached;
};
```

### 4.2 增量更新策略
```javascript
// 增量更新实现
const incrementalUpdate = async (key, updates) => {
  // 获取现有数据
  const current = await load(key);
  
  // 深度合并
  const updated = deepMerge(current, updates);
  
  // 计算差异
  const diff = calculateDiff(current, updated);
  
  // 仅在有变化时更新
  if (diff.hasChanges) {
    await save(key, updated);
    emit('dataChanged', { key, diff });
  }
};
```

### 4.3 缓存机制

#### 4.3.1 内存缓存层
```javascript
class MemoryCache {
  constructor(maxSize = 100, ttl = 300000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl; // 5分钟
  }
  
  set(key, value) {
    // LRU策略
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      hits: 0
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    // 更新访问信息
    item.hits++;
    item.timestamp = Date.now();
    
    // LRU: 移到末尾
    this.cache.delete(key);
    this.cache.set(key, item);
    
    return item.value;
  }
}
```

#### 4.3.2 缓存失效策略
- **TTL过期**: 固定时间后自动失效
- **LRU淘汰**: 缓存满时淘汰最少使用
- **主动失效**: 数据更新时主动清除
- **版本控制**: 数据版本变化时失效

#### 4.3.3 缓存同步机制
```javascript
// 多标签页缓存同步
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    for (const [key, { oldValue, newValue }] of Object.entries(changes)) {
      // 更新本地缓存
      cache.set(key, newValue);
      
      // 通知其他模块
      emit('storageChanged', { key, oldValue, newValue });
    }
  }
});
```

## 5. 数据完整性保障

### 5.1 事务性操作设计
```javascript
class Transaction {
  constructor(storageManager) {
    this.id = generateId();
    this.operations = [];
    this.snapshot = null;
    this.storage = storageManager;
  }
  
  async begin() {
    // 创建数据快照
    this.snapshot = await this.storage.createSnapshot();
  }
  
  add(operation) {
    this.operations.push(operation);
  }
  
  async commit() {
    try {
      // 执行所有操作
      for (const op of this.operations) {
        await this.executeOperation(op);
      }
      
      // 清理快照
      this.snapshot = null;
    } catch (error) {
      // 回滚
      await this.rollback();
      throw error;
    }
  }
  
  async rollback() {
    if (this.snapshot) {
      await this.storage.restoreSnapshot(this.snapshot);
    }
  }
}
```

### 5.2 数据版本控制
```javascript
// 版本迁移管理
const migrations = {
  '1.0.0': async (data) => {
    // 初始版本
    return data;
  },
  
  '1.1.0': async (data) => {
    // 添加新字段
    data.settings.newFeature = true;
    return data;
  },
  
  '2.0.0': async (data) => {
    // 重构数据结构
    data.windows = migrateWindowsV2(data.windows);
    return data;
  }
};

// 执行迁移
const migrate = async (data, fromVersion, toVersion) => {
  let currentData = data;
  const versions = Object.keys(migrations).sort();
  
  for (const version of versions) {
    if (version > fromVersion && version <= toVersion) {
      currentData = await migrations[version](currentData);
    }
  }
  
  return currentData;
};
```

### 5.3 数据迁移方案
1. **版本检测**: 启动时检查数据版本
2. **增量迁移**: 按版本顺序执行迁移
3. **备份原数据**: 迁移前自动备份
4. **验证机制**: 迁移后验证数据完整性
5. **回滚支持**: 迁移失败时恢复原数据

### 5.4 备份机制

#### 5.4.1 自动备份
```javascript
// 自动备份配置
const autoBackupConfig = {
  enabled: true,
  interval: 86400000, // 24小时
  maxBackups: 7,
  compression: true
};

// 自动备份任务
const scheduleAutoBackup = () => {
  setInterval(async () => {
    if (autoBackupConfig.enabled) {
      await createBackup('auto');
    }
  }, autoBackupConfig.interval);
};
```

#### 5.4.2 手动备份
```javascript
// 创建备份
const createBackup = async (type = 'manual') => {
  const backup = {
    id: generateId(),
    type,
    timestamp: Date.now(),
    version: CURRENT_VERSION,
    data: await exportAllData()
  };
  
  // 压缩数据
  if (autoBackupConfig.compression) {
    backup.data = compress(backup.data);
  }
  
  // 存储备份
  await saveBackup(backup);
  
  // 清理旧备份
  await cleanOldBackups();
  
  return backup.id;
};

// 恢复备份
const restoreBackup = async (backupId) => {
  const backup = await loadBackup(backupId);
  
  // 解压数据
  let data = backup.data;
  if (backup.compressed) {
    data = decompress(data);
  }
  
  // 版本迁移
  if (backup.version !== CURRENT_VERSION) {
    data = await migrate(data, backup.version, CURRENT_VERSION);
  }
  
  // 恢复数据
  await importAllData(data);
};
```

#### 5.4.3 备份轮转
```javascript
// 清理旧备份
const cleanOldBackups = async () => {
  const backups = await listBackups();
  
  // 按时间排序
  backups.sort((a, b) => b.timestamp - a.timestamp);
  
  // 保留策略
  const toKeep = [];
  const toDelete = [];
  
  // 保留最近N个
  toKeep.push(...backups.slice(0, autoBackupConfig.maxBackups));
  
  // 保留重要备份（手动创建）
  for (const backup of backups) {
    if (backup.type === 'manual' && !toKeep.includes(backup)) {
      toKeep.push(backup);
    }
  }
  
  // 删除其余
  for (const backup of backups) {
    if (!toKeep.includes(backup)) {
      toDelete.push(backup);
    }
  }
  
  // 执行删除
  await Promise.all(toDelete.map(b => deleteBackup(b.id)));
};
```

## 6. 存储限制处理

### 6.1 chrome.storage.local限制
- **存储容量**: 5MB (5,242,880 bytes)
- **单项限制**: 无
- **操作限制**: 每小时1,800次，每分钟120次

### 6.2 存储空间监控
```javascript
class StorageMonitor {
  constructor(threshold = 0.8) {
    this.threshold = threshold; // 80%警戒线
    this.maxSize = 5242880; // 5MB
  }
  
  async checkUsage() {
    const bytesInUse = await chrome.storage.local.getBytesInUse();
    const usage = bytesInUse / this.maxSize;
    
    return {
      bytesInUse,
      bytesAvailable: this.maxSize - bytesInUse,
      usagePercent: usage * 100,
      isWarning: usage >= this.threshold,
      isCritical: usage >= 0.95
    };
  }
  
  async startMonitoring() {
    // 定期检查
    setInterval(async () => {
      const status = await this.checkUsage();
      
      if (status.isWarning) {
        emit('storageWarning', status);
      }
      
      if (status.isCritical) {
        emit('storageCritical', status);
        await this.handleCriticalStorage();
      }
    }, 60000); // 每分钟检查
  }
}
```

### 6.3 数据清理策略
```javascript
// 清理策略配置
const cleanupStrategies = {
  // 清理过期数据
  expiredData: async () => {
    const now = Date.now();
    const expired = [];
    
    // 查找过期标签页（30天未访问）
    const tabs = await load('tabs');
    for (const [id, tab] of Object.entries(tabs)) {
      if (now - tab.updatedAt > 30 * 24 * 60 * 60 * 1000) {
        expired.push(id);
      }
    }
    
    return expired;
  },
  
  // 清理重复数据
  duplicateData: async () => {
    const duplicates = [];
    const seen = new Set();
    
    const tabs = await load('tabs');
    for (const [id, tab] of Object.entries(tabs)) {
      const key = `${tab.url}-${tab.windowId}`;
      if (seen.has(key)) {
        duplicates.push(id);
      }
      seen.add(key);
    }
    
    return duplicates;
  },
  
  // 清理大型数据
  largeData: async () => {
    const large = [];
    const tabs = await load('tabs');
    
    for (const [id, tab] of Object.entries(tabs)) {
      // 清理过大的元数据
      if (JSON.stringify(tab.metadata).length > 1024) {
        large.push({
          id,
          action: 'compress'
        });
      }
    }
    
    return large;
  }
};
```

### 6.4 压缩方案
```javascript
// 数据压缩工具
class DataCompressor {
  // 压缩文本数据
  compressText(text) {
    // 使用LZ-string或pako
    return LZString.compress(text);
  }
  
  // 压缩JSON数据
  compressJSON(data) {
    // 移除空白
    const minified = JSON.stringify(data);
    // 压缩
    return this.compressText(minified);
  }
  
  // 压缩URL
  compressURL(url) {
    // 移除协议
    url = url.replace(/^https?:\/\//, '');
    // 移除www
    url = url.replace(/^www\./, '');
    // 截断过长路径
    if (url.length > 200) {
      url = url.substring(0, 200) + '...';
    }
    return url;
  }
  
  // 智能压缩
  smartCompress(data) {
    const compressed = { ...data };
    
    // 压缩URL
    if (compressed.url) {
      compressed.url = this.compressURL(compressed.url);
    }
    
    // 压缩大型字段
    for (const [key, value] of Object.entries(compressed)) {
      if (typeof value === 'string' && value.length > 500) {
        compressed[key] = this.compressText(value);
        compressed[`${key}_compressed`] = true;
      }
    }
    
    return compressed;
  }
}
```

## 7. 与其他模块的接口

### 7.1 统一数据访问接口
```javascript
// 数据访问层
class DataAccessLayer {
  constructor(storageManager) {
    this.storage = storageManager;
  }
  
  // 窗口操作
  async getWindow(windowId) {
    const windows = await this.storage.load('windows');
    return windows[windowId];
  }
  
  async saveWindow(window) {
    const windows = await this.storage.load('windows') || {};
    windows[window.id] = window;
    await this.storage.save('windows', windows);
  }
  
  async deleteWindow(windowId) {
    const windows = await this.storage.load('windows') || {};
    delete windows[windowId];
    await this.storage.save('windows', windows);
    
    // 清理相关标签页
    await this.deleteTabsByWindow(windowId);
  }
  
  // 标签页操作
  async getTab(tabId) {
    const tabs = await this.storage.load('tabs');
    return tabs[tabId];
  }
  
  async saveTabs(tabs) {
    const stored = await this.storage.load('tabs') || {};
    for (const tab of tabs) {
      stored[tab.id] = tab;
    }
    await this.storage.save('tabs', stored);
  }
  
  // 查询操作
  async queryTabs(filter) {
    const tabs = await this.storage.load('tabs') || {};
    return Object.values(tabs).filter(tab => {
      for (const [key, value] of Object.entries(filter)) {
        if (tab[key] !== value) return false;
      }
      return true;
    });
  }
}
```

### 7.2 变更通知机制
```javascript
// 事件发射器
class StorageEventEmitter extends EventTarget {
  emit(eventName, data) {
    this.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  }
  
  on(eventName, handler) {
    this.addEventListener(eventName, handler);
  }
  
  off(eventName, handler) {
    this.removeEventListener(eventName, handler);
  }
}

// 变更监听器
const setupChangeListeners = () => {
  // Chrome存储变更
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
      for (const [key, change] of Object.entries(changes)) {
        emitter.emit('dataChanged', {
          key,
          oldValue: change.oldValue,
          newValue: change.newValue,
          timestamp: Date.now()
        });
      }
    }
  });
  
  // 自定义变更事件
  emitter.on('dataChanged', ({ detail }) => {
    // 更新缓存
    cache.invalidate(detail.key);
    
    // 通知UI更新
    if (detail.key === 'windows' || detail.key === 'tabs') {
      emitter.emit('uiUpdate', detail);
    }
    
    // 记录变更日志
    logger.log('Data changed', detail);
  });
};
```

### 7.3 数据同步协议
```javascript
// 同步管理器
class SyncManager {
  constructor(storage) {
    this.storage = storage;
    this.syncQueue = [];
    this.syncing = false;
  }
  
  // 添加同步任务
  addToSync(operation) {
    this.syncQueue.push({
      id: generateId(),
      operation,
      timestamp: Date.now(),
      retries: 0
    });
    
    this.processSyncQueue();
  }
  
  // 处理同步队列
  async processSyncQueue() {
    if (this.syncing || this.syncQueue.length === 0) {
      return;
    }
    
    this.syncing = true;
    
    while (this.syncQueue.length > 0) {
      const task = this.syncQueue.shift();
      
      try {
        await this.executeSync(task);
      } catch (error) {
        // 重试机制
        if (task.retries < 3) {
          task.retries++;
          this.syncQueue.push(task);
        } else {
          this.handleSyncError(task, error);
        }
      }
    }
    
    this.syncing = false;
  }
  
  // 执行同步
  async executeSync(task) {
    const { operation } = task;
    
    switch (operation.type) {
      case 'save':
        await this.storage.save(operation.key, operation.data);
        break;
        
      case 'delete':
        await this.storage.remove(operation.key);
        break;
        
      case 'batch':
        await this.storage.batchSave(operation.items);
        break;
        
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
    
    // 发送同步完成事件
    emitter.emit('syncCompleted', task);
  }
}
```

## 8. 性能优化方案

### 8.1 延迟加载
- 按需加载数据，避免启动时加载全部
- 实现数据分页，大数据集分批加载
- 预加载用户可能需要的数据

### 8.2 数据索引
- 为常用查询建立内存索引
- 维护数据统计信息，加速聚合查询
- 实现倒排索引，支持全文搜索

### 8.3 写入优化
- 合并连续的写操作
- 实现写入缓冲区
- 异步写入，不阻塞UI

## 9. 错误处理机制

### 9.1 错误类型
- **存储配额超限**: QuotaExceededError
- **数据损坏**: DataCorruptionError
- **版本不兼容**: VersionMismatchError
- **网络错误**: NetworkError
- **权限错误**: PermissionError

### 9.2 错误恢复
```javascript
// 错误恢复策略
const errorRecoveryStrategies = {
  QuotaExceededError: async () => {
    // 1. 执行紧急清理
    await emergencyCleanup();
    // 2. 通知用户
    notifyUser('存储空间不足，已自动清理部分数据');
    // 3. 重试操作
    return { retry: true };
  },
  
  DataCorruptionError: async (error) => {
    // 1. 尝试修复
    const fixed = await attemptDataRepair(error.key);
    if (fixed) return { retry: true };
    
    // 2. 从备份恢复
    const restored = await restoreFromLatestBackup(error.key);
    if (restored) return { retry: true };
    
    // 3. 重置数据
    await resetData(error.key);
    return { retry: false, reset: true };
  }
};
```

### 9.3 日志记录
```javascript
// 日志系统
class StorageLogger {
  log(level, message, data) {
    const entry = {
      timestamp: Date.now(),
      level,
      message,
      data,
      stack: new Error().stack
    };
    
    // 控制台输出
    console[level](message, data);
    
    // 持久化重要日志
    if (level === 'error' || level === 'warn') {
      this.persistLog(entry);
    }
  }
  
  async persistLog(entry) {
    const logs = await load('_logs') || [];
    logs.push(entry);
    
    // 限制日志数量
    if (logs.length > 1000) {
      logs.splice(0, logs.length - 1000);
    }
    
    await save('_logs', logs);
  }
}
```

## 10. 测试策略

### 10.1 单元测试
- 测试所有API方法
- 测试错误处理
- 测试边界条件

### 10.2 集成测试
- 测试与其他模块的交互
- 测试数据同步
- 测试并发操作

### 10.3 性能测试
- 测试大数据量操作
- 测试并发读写
- 测试存储限制

### 10.4 压力测试
- 模拟存储满载
- 模拟高频操作
- 模拟数据损坏

## 11. 未来扩展

### 11.1 云同步支持
- 支持多设备同步
- 冲突解决机制
- 选择性同步

### 11.2 数据加密
- 敏感数据加密存储
- 密钥管理
- 端到端加密

### 11.3 高级查询
- SQL-like查询语言
- 复杂过滤条件
- 聚合函数支持

### 11.4 数据分析
- 使用统计
- 性能分析
- 存储优化建议