# 窗口/标签组管理模块需求文档

## 1. 模块概述

### 1.1 核心概念
- **以窗口为单位管理标签页**：将相关的标签页组织在同一个窗口中，实现结构化管理
- **窗口组的概念**：窗口是标签页的容器，提供分组、分类和批量操作的能力
- **作用**：
  - 提供更清晰的标签页组织结构
  - 支持批量操作和管理
  - 保持浏览上下文的连续性
  - 提升多任务处理效率

### 1.2 模块职责
- 管理窗口的生命周期（创建、更新、删除）
- 维护窗口与标签页的关联关系
- 提供窗口级别的操作接口
- 处理窗口间的交互（合并、拆分、移动）

## 2. 数据模型定义

### 2.1 Window 数据结构
```javascript
Window {
  // 基础标识
  windowId: string,        // UUID，窗口唯一标识符
  name: string,           // 窗口名称，用户可自定义
  
  // 标签页管理
  tabs: Tab[],            // 该窗口包含的所有标签页数组
  tabCount: number,       // 标签页数量，用于快速统计
  
  // 时间戳
  createdAt: timestamp,   // 窗口创建时间
  updatedAt: timestamp,   // 最后更新时间
  lastAccessedAt: timestamp, // 最后访问时间
  
  // 显示属性
  isCollapsed: boolean,   // 折叠状态，控制UI显示
  color: string,          // 主题颜色，格式：#RRGGBB
  icon: string,           // 图标标识符（可选）
  position: number,       // 排序位置，用于自定义排序
  
  // 元数据
  metadata: {
    originalWindowId: number,  // 原始浏览器窗口ID
    screenInfo: {             // 屏幕信息（可选）
      width: number,
      height: number,
      left: number,
      top: number,
      state: string           // normal, maximized, minimized, fullscreen
    }
  }
}
```

### 2.2 数据约束
- `windowId`: 必须是有效的UUID v4格式
- `name`: 最大长度100字符，不能为空
- `color`: 必须是有效的6位16进制颜色值
- `position`: 非负整数，用于确定显示顺序
- `tabs`: 数组元素必须是有效的Tab对象

## 3. API接口定义

### 3.1 基础CRUD操作

#### createWindow(windowData)
创建新窗口
```javascript
/**
 * @param {Object} windowData - 窗口初始数据
 * @param {string} windowData.name - 窗口名称
 * @param {string} [windowData.color] - 主题颜色
 * @param {string} [windowData.icon] - 图标
 * @param {Tab[]} [windowData.tabs] - 初始标签页
 * @returns {Promise<Window>} 创建的窗口对象
 */
async function createWindow(windowData) {
  // 1. 验证输入数据
  // 2. 生成windowId (UUID)
  // 3. 设置默认值和时间戳
  // 4. 保存到存储
  // 5. 返回完整的Window对象
}
```

#### updateWindow(windowId, updates)
更新窗口信息
```javascript
/**
 * @param {string} windowId - 窗口ID
 * @param {Object} updates - 要更新的字段
 * @returns {Promise<Window>} 更新后的窗口对象
 */
async function updateWindow(windowId, updates) {
  // 1. 验证windowId存在
  // 2. 验证更新数据合法性
  // 3. 更新updatedAt时间戳
  // 4. 保存更改
  // 5. 返回更新后的对象
}
```

#### deleteWindow(windowId)
删除窗口
```javascript
/**
 * @param {string} windowId - 窗口ID
 * @returns {Promise<boolean>} 删除是否成功
 */
async function deleteWindow(windowId) {
  // 1. 验证窗口存在
  // 2. 处理窗口内的标签页（移到默认窗口或删除）
  // 3. 删除窗口数据
  // 4. 更新相关索引
  // 5. 返回操作结果
}
```

#### getWindow(windowId)
获取单个窗口
```javascript
/**
 * @param {string} windowId - 窗口ID
 * @returns {Promise<Window>} 窗口对象
 */
async function getWindow(windowId) {
  // 1. 从存储获取窗口数据
  // 2. 加载关联的标签页
  // 3. 返回完整的Window对象
}
```

#### getAllWindows()
获取所有窗口
```javascript
/**
 * @returns {Promise<Window[]>} 所有窗口的数组
 */
async function getAllWindows() {
  // 1. 获取所有窗口数据
  // 2. 按position字段排序
  // 3. 批量加载标签页数据
  // 4. 返回窗口数组
}
```

### 3.2 窗口间操作

#### moveTabToWindow(tabId, targetWindowId)
移动标签页到目标窗口
```javascript
/**
 * @param {string} tabId - 标签页ID
 * @param {string} targetWindowId - 目标窗口ID
 * @returns {Promise<boolean>} 操作是否成功
 */
async function moveTabToWindow(tabId, targetWindowId) {
  // 1. 验证标签页和目标窗口存在
  // 2. 从原窗口移除标签页
  // 3. 添加到目标窗口
  // 4. 更新两个窗口的tabCount
  // 5. 更新标签页的windowId
  // 6. 保存所有更改
}
```

#### mergeWindows(sourceWindowId, targetWindowId)
合并窗口
```javascript
/**
 * @param {string} sourceWindowId - 源窗口ID
 * @param {string} targetWindowId - 目标窗口ID
 * @returns {Promise<Window>} 合并后的目标窗口
 */
async function mergeWindows(sourceWindowId, targetWindowId) {
  // 1. 验证两个窗口都存在
  // 2. 将源窗口的所有标签页移到目标窗口
  // 3. 更新标签页的windowId
  // 4. 删除源窗口
  // 5. 返回更新后的目标窗口
}
```

#### splitWindow(windowId, tabIds)
拆分窗口
```javascript
/**
 * @param {string} windowId - 源窗口ID
 * @param {string[]} tabIds - 要拆分出去的标签页ID数组
 * @returns {Promise<Window>} 新创建的窗口
 */
async function splitWindow(windowId, tabIds) {
  // 1. 验证窗口和标签页存在
  // 2. 创建新窗口
  // 3. 将指定标签页移到新窗口
  // 4. 更新原窗口和新窗口的数据
  // 5. 返回新窗口对象
}
```

## 4. 窗口组织功能

### 4.1 拖拽排序逻辑
```javascript
/**
 * 处理窗口拖拽排序
 * @param {string} draggedWindowId - 被拖拽的窗口ID
 * @param {number} newPosition - 新位置
 */
async function reorderWindow(draggedWindowId, newPosition) {
  // 1. 获取所有窗口并按position排序
  // 2. 移除被拖拽的窗口
  // 3. 在新位置插入窗口
  // 4. 重新计算所有窗口的position值
  // 5. 批量更新position
}
```

### 4.2 折叠/展开状态管理
```javascript
/**
 * 切换窗口折叠状态
 * @param {string} windowId - 窗口ID
 */
async function toggleWindowCollapse(windowId) {
  // 1. 获取当前折叠状态
  // 2. 切换isCollapsed值
  // 3. 更新lastAccessedAt
  // 4. 保存状态
}

/**
 * 批量设置折叠状态
 * @param {boolean} collapsed - 目标状态
 */
async function setAllWindowsCollapsed(collapsed) {
  // 1. 获取所有窗口
  // 2. 批量更新isCollapsed
  // 3. 保存更改
}
```

### 4.3 窗口颜色和图标管理
```javascript
/**
 * 设置窗口主题
 * @param {string} windowId - 窗口ID
 * @param {Object} theme - 主题配置
 * @param {string} [theme.color] - 颜色值
 * @param {string} [theme.icon] - 图标标识
 */
async function setWindowTheme(windowId, theme) {
  // 1. 验证颜色格式（#RRGGBB）
  // 2. 验证图标是否在可用列表中
  // 3. 更新窗口主题属性
  // 4. 保存更改
}

// 预定义的颜色主题
const COLOR_THEMES = {
  work: '#2563eb',      // 蓝色 - 工作
  personal: '#16a34a',  // 绿色 - 个人
  research: '#7c3aed',  // 紫色 - 研究
  shopping: '#dc2626',  // 红色 - 购物
  entertainment: '#f59e0b' // 黄色 - 娱乐
};
```

## 5. 标签页与窗口的关联管理

### 5.1 维护标签页的windowId
```javascript
/**
 * 更新标签页的窗口归属
 * @param {string} tabId - 标签页ID
 * @param {string} windowId - 新的窗口ID
 */
async function updateTabWindowAssociation(tabId, windowId) {
  // 1. 验证标签页和窗口存在
  // 2. 更新标签页的windowId字段
  // 3. 更新原窗口的tabs数组和tabCount
  // 4. 更新新窗口的tabs数组和tabCount
  // 5. 保存所有更改
}
```

### 5.2 窗口删除时的标签页处理
```javascript
/**
 * 处理窗口删除时的标签页
 * @param {string} windowId - 要删除的窗口ID
 * @param {Object} options - 处理选项
 * @param {string} [options.action] - 'delete' | 'move' | 'archive'
 * @param {string} [options.targetWindowId] - 移动目标窗口ID
 */
async function handleWindowDeletionTabs(windowId, options = {}) {
  const { action = 'move', targetWindowId } = options;
  
  switch (action) {
    case 'delete':
      // 删除所有标签页
      break;
    case 'move':
      // 移动到指定窗口或默认窗口
      break;
    case 'archive':
      // 归档标签页
      break;
  }
}
```

### 5.3 标签页移动时的数据同步
```javascript
/**
 * 同步标签页移动操作
 * @param {string} tabId - 标签页ID
 * @param {string} fromWindowId - 源窗口ID
 * @param {string} toWindowId - 目标窗口ID
 * @param {number} [position] - 在目标窗口中的位置
 */
async function syncTabMove(tabId, fromWindowId, toWindowId, position) {
  // 1. 开始事务
  // 2. 从源窗口移除标签页
  // 3. 在目标窗口指定位置插入
  // 4. 更新标签页的windowId
  // 5. 更新两个窗口的元数据
  // 6. 提交事务
}
```

## 6. 批量操作支持

### 6.1 批量移动标签到窗口
```javascript
/**
 * 批量移动标签页
 * @param {string[]} tabIds - 标签页ID数组
 * @param {string} targetWindowId - 目标窗口ID
 * @param {Object} options - 移动选项
 * @param {boolean} [options.preserveOrder] - 保持原顺序
 * @param {number} [options.insertPosition] - 插入位置
 */
async function batchMoveTabsToWindow(tabIds, targetWindowId, options = {}) {
  // 1. 验证所有标签页存在
  // 2. 按原窗口分组标签页
  // 3. 批量执行移动操作
  // 4. 更新所有相关窗口的数据
  // 5. 返回操作结果统计
}
```

### 6.2 合并多个窗口
```javascript
/**
 * 合并多个窗口到一个目标窗口
 * @param {string[]} sourceWindowIds - 源窗口ID数组
 * @param {string} targetWindowId - 目标窗口ID
 * @param {Object} options - 合并选项
 * @param {boolean} [options.keepWindowOrder] - 保持窗口顺序
 * @param {boolean} [options.deleteSources] - 删除源窗口
 */
async function batchMergeWindows(sourceWindowIds, targetWindowId, options = {}) {
  // 1. 验证所有窗口存在
  // 2. 确定合并顺序
  // 3. 逐个合并窗口
  // 4. 处理源窗口（删除或清空）
  // 5. 返回合并后的窗口
}
```

### 6.3 克隆窗口组
```javascript
/**
 * 克隆窗口及其所有标签页
 * @param {string} windowId - 要克隆的窗口ID
 * @param {Object} options - 克隆选项
 * @param {string} [options.name] - 新窗口名称
 * @param {boolean} [options.includeHistory] - 包含浏览历史
 * @param {boolean} [options.openInBrowser] - 在浏览器中打开
 */
async function cloneWindow(windowId, options = {}) {
  // 1. 获取源窗口完整数据
  // 2. 创建新窗口
  // 3. 克隆所有标签页
  // 4. 保持标签页顺序和分组
  // 5. 可选：在浏览器中重新打开
  // 6. 返回新窗口对象
}
```

## 7. 实现注意事项

### 7.1 性能优化
- 使用批量操作减少存储访问次数
- 实现窗口和标签页的懒加载
- 缓存常用的窗口数据
- 使用索引加速查询操作

### 7.2 数据一致性
- 使用事务确保多步操作的原子性
- 实现双向关联的同步更新
- 定期验证数据完整性
- 提供数据修复工具

### 7.3 错误处理
- 所有API都应该有完善的错误处理
- 提供有意义的错误消息
- 实现操作回滚机制
- 记录关键操作日志

### 7.4 扩展性考虑
- 预留自定义属性字段
- 支持插件扩展窗口功能
- 提供事件钩子机制
- 保持API的向后兼容性