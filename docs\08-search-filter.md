# 搜索过滤模块需求文档

## 1. 模块概述

搜索过滤模块是标签管理系统的核心功能组件，旨在为用户提供强大且灵活的标签检索能力。该模块支持多维度搜索、智能过滤和高效的结果展示，帮助用户快速定位和管理海量标签数据。

### 1.1 核心功能
- 多字段全文搜索
- 灵活的过滤条件组合
- 实时搜索结果更新
- 搜索历史和建议
- 高性能索引机制

### 1.2 设计目标
- **易用性**：简洁直观的搜索界面
- **准确性**：精确的搜索结果匹配
- **性能**：毫秒级搜索响应
- **扩展性**：支持自定义搜索规则

## 2. 搜索功能设计

### 2.1 搜索类型

#### 2.1.1 标题搜索
- **功能描述**：在标签页标题中进行模糊匹配搜索
- **匹配规则**：
  - 不区分大小写
  - 支持部分匹配
  - 支持多语言内容
  - 特殊字符处理

#### 2.1.2 URL搜索
- **功能描述**：搜索标签页的URL地址
- **搜索范围**：
  - 域名匹配（example.com）
  - 路径匹配（/path/to/page）
  - 查询参数匹配（?param=value）
  - 协议匹配（http/https）

#### 2.1.3 标签搜索
- **功能描述**：基于用户添加的标签进行搜索
- **匹配方式**：
  - 精确标签匹配
  - 标签前缀匹配
  - 多标签组合（AND/OR）

#### 2.1.4 备注搜索
- **功能描述**：在用户备注内容中搜索
- **特点**：
  - 全文搜索
  - 支持中文分词
  - 关键词高亮

### 2.2 搜索语法

#### 2.2.1 基础语法
```
简单搜索：youtube
精确匹配："exact phrase"
排除搜索：chrome -extension
通配符：doc*
```

#### 2.2.2 高级语法
```
字段指定：title:youtube url:google.com
组合条件：(youtube OR bilibili) AND tutorial
标签过滤：tag:work tag:important
时间范围：created:2024-01-01..2024-12-31
```

#### 2.2.3 搜索操作符
| 操作符 | 说明 | 示例 |
|-------|------|------|
| AND | 与操作 | react AND tutorial |
| OR | 或操作 | vue OR react |
| NOT / - | 非操作 | python -django |
| " " | 精确匹配 | "machine learning" |
| * | 通配符 | prog* |
| : | 字段指定 | title:chrome |

## 3. 过滤器功能

### 3.1 时间过滤

#### 3.1.1 预设时间范围
- **今天**：当天00:00至当前时间
- **昨天**：前一天全天
- **最近7天**：过去7天
- **最近30天**：过去30天
- **本月**：当月1日至今
- **上月**：上个月全月

#### 3.1.2 自定义时间范围
```javascript
{
  startDate: '2024-01-01T00:00:00',
  endDate: '2024-12-31T23:59:59',
  includeTime: boolean // 是否包含具体时间
}
```

### 3.2 窗口过滤

#### 3.2.1 窗口组筛选
- 显示所有窗口列表
- 支持多选窗口
- 显示每个窗口的标签数量
- 窗口名称搜索

#### 3.2.2 窗口过滤选项
```javascript
{
  windowIds: string[],        // 选中的窗口ID列表
  includeAll: boolean,       // 是否包含所有窗口
  excludeClosed: boolean     // 排除已关闭窗口
}
```

### 3.3 状态过滤

#### 3.3.1 固定状态
- 仅显示已固定标签
- 仅显示未固定标签
- 全部显示

#### 3.3.2 标签状态
- 有标签的标签页
- 无标签的标签页
- 特定标签筛选

#### 3.3.3 备注状态
- 有备注的标签页
- 无备注的标签页
- 备注字数范围

## 4. 搜索API设计

### 4.1 核心接口定义

```typescript
// 搜索选项接口
interface SearchOptions {
  // 搜索查询
  query: string;
  
  // 过滤条件
  filters: {
    // 窗口过滤
    windows: string[];
    
    // 时间范围
    dateRange: {
      start: Date;
      end: Date;
      preset?: 'today' | 'yesterday' | 'week' | 'month' | 'custom';
    };
    
    // 标签过滤
    tags: {
      include: string[];    // 包含的标签
      exclude: string[];    // 排除的标签
      mode: 'all' | 'any'; // 匹配模式
    };
    
    // 状态过滤
    status: {
      isPinned?: boolean;
      hasTags?: boolean;
      hasNotes?: boolean;
    };
  };
  
  // 排序选项
  sort: {
    field: 'createdAt' | 'updatedAt' | 'title' | 'url' | 'visitCount';
    order: 'asc' | 'desc';
  };
  
  // 分页选项
  pagination: {
    page: number;
    pageSize: number;
    offset?: number;
  };
  
  // 搜索配置
  config: {
    caseSensitive: boolean;
    fuzzyMatch: boolean;
    highlightResults: boolean;
  };
}

// 搜索结果接口
interface SearchResult {
  // 结果数据
  data: TabItem[];
  
  // 分页信息
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  
  // 搜索元数据
  metadata: {
    query: string;
    executionTime: number;
    matchedFields: string[];
    suggestions: string[];
  };
  
  // 聚合数据
  aggregations: {
    byWindow: Record<string, number>;
    byTag: Record<string, number>;
    byDate: Record<string, number>;
  };
}
```

### 4.2 API方法

```typescript
// 主搜索方法
async function searchTabs(options: SearchOptions): Promise<SearchResult> {
  // 1. 解析搜索查询
  const parsedQuery = parseSearchQuery(options.query);
  
  // 2. 构建搜索条件
  const searchConditions = buildSearchConditions(parsedQuery, options.filters);
  
  // 3. 执行搜索
  const results = await executeSearch(searchConditions);
  
  // 4. 应用排序和分页
  const paginatedResults = applyPaginationAndSort(results, options);
  
  // 5. 生成搜索建议
  const suggestions = generateSuggestions(parsedQuery, results);
  
  // 6. 返回结果
  return formatSearchResult(paginatedResults, suggestions);
}

// 搜索建议方法
async function getSearchSuggestions(prefix: string): Promise<string[]> {
  // 基于搜索历史和索引返回建议
  return await searchIndex.getSuggestions(prefix);
}

// 保存搜索历史
async function saveSearchHistory(query: string, resultCount: number): Promise<void> {
  await searchHistory.add({
    query,
    timestamp: new Date(),
    resultCount
  });
}
```

## 5. 搜索优化

### 5.1 搜索索引

#### 5.1.1 索引结构
```javascript
{
  // 倒排索引
  invertedIndex: {
    'keyword': [tabId1, tabId2, ...],
    // ...
  },
  
  // 字段索引
  fieldIndex: {
    title: Map<string, Set<tabId>>,
    url: Map<string, Set<tabId>>,
    tags: Map<string, Set<tabId>>,
    notes: Map<string, Set<tabId>>
  },
  
  // 时间索引
  timeIndex: {
    byDay: Map<string, Set<tabId>>,
    byMonth: Map<string, Set<tabId>>
  }
}
```

#### 5.1.2 索引更新策略
- **实时更新**：标签创建、修改、删除时同步更新
- **批量更新**：定期重建索引（每天凌晨）
- **增量更新**：只更新变化的部分
- **索引压缩**：定期清理无效索引

### 5.2 搜索结果缓存

#### 5.2.1 缓存策略
```typescript
interface CacheStrategy {
  // 缓存键生成
  generateKey(options: SearchOptions): string;
  
  // 缓存有效期
  ttl: number; // 秒
  
  // 最大缓存数
  maxSize: number;
  
  // 淘汰策略
  evictionPolicy: 'LRU' | 'LFU' | 'FIFO';
}
```

#### 5.2.2 缓存实现
```typescript
class SearchCache {
  private cache: Map<string, CachedResult>;
  
  async get(key: string): Promise<SearchResult | null> {
    const cached = this.cache.get(key);
    if (cached && !this.isExpired(cached)) {
      return cached.result;
    }
    return null;
  }
  
  async set(key: string, result: SearchResult): Promise<void> {
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
    this.evictIfNeeded();
  }
}
```

### 5.3 搜索建议功能

#### 5.3.1 建议来源
- 搜索历史（权重：40%）
- 热门搜索（权重：30%）
- 标签/标题匹配（权重：30%）

#### 5.3.2 建议算法
```typescript
function generateSuggestions(prefix: string): string[] {
  const suggestions = [];
  
  // 1. 从搜索历史获取
  const historySuggestions = searchHistory
    .filter(item => item.query.startsWith(prefix))
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 5);
  
  // 2. 从索引获取
  const indexSuggestions = searchIndex
    .getTermsStartingWith(prefix)
    .slice(0, 5);
  
  // 3. 合并和排序
  return mergeSuggestions(historySuggestions, indexSuggestions);
}
```

### 5.4 搜索历史记录

#### 5.4.1 历史数据结构
```typescript
interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  clickedResults: string[];
  searchDuration: number;
}
```

#### 5.4.2 历史管理
- 最多保存100条历史记录
- 支持清除历史
- 支持导出历史
- 隐私模式（不记录历史）

## 6. UI集成

### 6.1 搜索框组件

#### 6.1.1 组件功能
- 实时搜索建议下拉
- 搜索历史快速访问
- 清除按钮
- 搜索语法提示
- 快捷键支持（Ctrl+F）

#### 6.1.2 组件接口
```typescript
interface SearchBoxProps {
  placeholder?: string;
  defaultValue?: string;
  onSearch: (query: string) => void;
  onSuggestionSelect: (suggestion: string) => void;
  showHistory?: boolean;
  debounceDelay?: number;
}
```

### 6.2 过滤器面板

#### 6.2.1 面板布局
```
┌─────────────────────────────┐
│ 时间范围                     │
│ [今天] [7天] [30天] [自定义] │
├─────────────────────────────┤
│ 窗口筛选                     │
│ ☑ Window 1 (23)             │
│ ☐ Window 2 (15)             │
│ ☑ Window 3 (42)             │
├─────────────────────────────┤
│ 状态过滤                     │
│ ☑ 已固定  ☐ 有标签  ☐ 有备注│
├─────────────────────────────┤
│ 标签筛选                     │
│ [工作] [学习] [娱乐] ...     │
└─────────────────────────────┘
```

#### 6.2.2 过滤器组件
```typescript
interface FilterPanelProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  availableTags: string[];
  windowList: WindowInfo[];
}
```

### 6.3 搜索结果高亮

#### 6.3.1 高亮策略
```typescript
function highlightSearchTerms(text: string, terms: string[]): ReactNode {
  let highlighted = text;
  
  terms.forEach(term => {
    const regex = new RegExp(`(${escapeRegex(term)})`, 'gi');
    highlighted = highlighted.replace(regex, '<mark>$1</mark>');
  });
  
  return <span dangerouslySetInnerHTML={{ __html: highlighted }} />;
}
```

#### 6.3.2 高亮样式
```css
.search-highlight {
  background-color: #ffeb3b;
  color: #000;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
}
```

### 6.4 实时搜索反馈

#### 6.4.1 加载状态
- 搜索中的加载动画
- 搜索进度指示器
- 取消搜索按钮

#### 6.4.2 结果统计
```typescript
interface SearchStats {
  totalResults: number;
  searchTime: number;
  matchedWindows: number;
  matchedTags: string[];
}
```

## 7. 性能考虑

### 7.1 防抖处理

```typescript
const debouncedSearch = useMemo(
  () => debounce((query: string) => {
    performSearch(query);
  }, 300),
  []
);
```

### 7.2 分页加载

#### 7.2.1 虚拟滚动
```typescript
interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  bufferSize: number;
  overscan: number;
}
```

#### 7.2.2 懒加载策略
- 初始加载20条
- 滚动到底部自动加载更多
- 预加载下一页数据

### 7.3 索引更新策略

#### 7.3.1 更新时机
- 标签页打开/关闭
- 标签/备注修改
- 批量导入完成
- 定时全量更新

#### 7.3.2 更新优化
```typescript
class IndexUpdater {
  private updateQueue: UpdateTask[] = [];
  private batchTimer: NodeJS.Timeout;
  
  scheduleUpdate(task: UpdateTask) {
    this.updateQueue.push(task);
    this.scheduleBatch();
  }
  
  private scheduleBatch() {
    clearTimeout(this.batchTimer);
    this.batchTimer = setTimeout(() => {
      this.processBatch();
    }, 100);
  }
  
  private async processBatch() {
    const tasks = [...this.updateQueue];
    this.updateQueue = [];
    await this.index.batchUpdate(tasks);
  }
}
```

### 7.4 大数据集优化

#### 7.4.1 性能指标
- 10万条记录搜索响应 < 100ms
- 索引更新时间 < 50ms
- 内存占用 < 100MB

#### 7.4.2 优化技术
1. **索引分片**
   - 按时间分片
   - 按窗口分片
   - 动态加载分片

2. **查询优化**
   - 查询计划缓存
   - 并行查询执行
   - 提前终止优化

3. **内存管理**
   - 索引压缩
   - 冷数据卸载
   - 内存映射文件

4. **Web Worker**
   ```typescript
   // 在 Web Worker 中执行搜索
   const searchWorker = new Worker('search.worker.js');
   
   searchWorker.postMessage({
     type: 'search',
     options: searchOptions
   });
   
   searchWorker.onmessage = (event) => {
     const { results } = event.data;
     updateUI(results);
   };
   ```

## 8. 搜索算法详解

### 8.1 文本匹配算法

#### 8.1.1 模糊匹配
```typescript
function fuzzyMatch(text: string, query: string): number {
  // 使用 Levenshtein 距离算法
  const distance = levenshteinDistance(text.toLowerCase(), query.toLowerCase());
  const maxLength = Math.max(text.length, query.length);
  return 1 - (distance / maxLength);
}
```

#### 8.1.2 分词处理
```typescript
class Tokenizer {
  // 中文分词
  tokenizeChinese(text: string): string[] {
    // 使用 jieba 分词算法
    return jieba.cut(text);
  }
  
  // 英文分词
  tokenizeEnglish(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0);
  }
}
```

### 8.2 排序算法

#### 8.2.1 相关性评分
```typescript
interface RelevanceScore {
  titleMatch: number;      // 标题匹配度 (0-1)
  urlMatch: number;        // URL匹配度 (0-1)
  tagMatch: number;        // 标签匹配度 (0-1)
  noteMatch: number;       // 备注匹配度 (0-1)
  recency: number;         // 时间新鲜度 (0-1)
  popularity: number;      // 访问频率 (0-1)
}

function calculateRelevance(tab: TabItem, query: string): number {
  const scores: RelevanceScore = {
    titleMatch: calculateTitleMatch(tab.title, query) * 0.3,
    urlMatch: calculateUrlMatch(tab.url, query) * 0.2,
    tagMatch: calculateTagMatch(tab.tags, query) * 0.2,
    noteMatch: calculateNoteMatch(tab.notes, query) * 0.15,
    recency: calculateRecency(tab.updatedAt) * 0.1,
    popularity: calculatePopularity(tab.visitCount) * 0.05
  };
  
  return Object.values(scores).reduce((sum, score) => sum + score, 0);
}
```

### 8.3 索引结构

#### 8.3.1 B+树索引
```typescript
class BPlusTreeIndex<T> {
  private root: BPlusNode<T>;
  private order: number;
  
  insert(key: string, value: T): void {
    // B+树插入算法
  }
  
  search(key: string): T[] {
    // B+树搜索算法
  }
  
  rangeSearch(start: string, end: string): T[] {
    // 范围查询
  }
}
```

#### 8.3.2 倒排索引
```typescript
class InvertedIndex {
  private index: Map<string, Set<string>>;
  private docFrequency: Map<string, number>;
  
  addDocument(docId: string, tokens: string[]): void {
    tokens.forEach(token => {
      if (!this.index.has(token)) {
        this.index.set(token, new Set());
      }
      this.index.get(token).add(docId);
    });
  }
  
  search(query: string[]): string[] {
    const results = query.map(term => 
      Array.from(this.index.get(term) || [])
    );
    
    // 计算交集
    return intersection(...results);
  }
}
```

## 9. 测试策略

### 9.1 单元测试
```typescript
describe('SearchModule', () => {
  test('should return exact matches first', async () => {
    const results = await searchTabs({
      query: 'React Tutorial',
      filters: {},
      sort: { field: 'relevance', order: 'desc' }
    });
    
    expect(results.data[0].title).toContain('React Tutorial');
  });
  
  test('should handle Chinese search correctly', async () => {
    const results = await searchTabs({
      query: '前端开发',
      filters: {},
      config: { fuzzyMatch: true }
    });
    
    expect(results.data.length).toBeGreaterThan(0);
  });
});
```

### 9.2 性能测试
```typescript
describe('SearchPerformance', () => {
  test('should search 100k records within 100ms', async () => {
    const startTime = performance.now();
    
    await searchTabs({
      query: 'test query',
      pagination: { page: 1, pageSize: 20 }
    });
    
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100);
  });
});
```

## 10. 未来扩展

### 10.1 AI增强搜索
- 语义搜索
- 自然语言查询
- 智能推荐

### 10.2 高级功能
- 保存搜索条件
- 搜索订阅通知
- 协同搜索过滤

### 10.3 性能提升
- GPU加速搜索
- 分布式索引
- 边缘计算优化