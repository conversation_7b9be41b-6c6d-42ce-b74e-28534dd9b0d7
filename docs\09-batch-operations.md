# 批量操作模块需求文档

## 1. 模块概述

批量操作模块是标签页管理系统的核心功能之一，旨在支持对多个标签页的批量操作，显著提高用户在处理大量标签页时的操作效率。该模块提供了灵活的选择机制、丰富的批量操作功能以及完善的撤销/重做支持。

### 主要目标
- 提供直观的多选机制
- 支持常见的批量操作场景
- 确保操作的安全性和可恢复性
- 提供实时的操作反馈

## 2. 选择机制

### 2.1 选择方式

#### 基础选择操作
- **单击选择**：点击标签页切换其选择状态
- **Shift+单击**：选择当前点击项与上次点击项之间的所有标签页
- **Ctrl/Cmd+单击**：添加或移除单个标签页到选择集合
- **全选快捷键**：Ctrl/Cmd+A 选择当前视图中的所有标签页

#### 高级选择功能
```javascript
interface SelectionMethods {
  // 按条件选择
  selectByDomain(domain: string): void
  selectByDateRange(start: Date, end: Date): void
  selectByTags(tags: string[]): void
  selectByWindow(windowId: string): void
  
  // 反选和清除
  invertSelection(): void
  clearSelection(): void
  
  // 选择扩展
  expandSelectionToWindow(): void
  expandSelectionToDomain(): void
}
```

### 2.2 选择状态管理

#### 状态数据结构
```javascript
interface SelectionState {
  selectedIds: Set<string>
  lastSelectedId: string | null
  selectionMode: 'single' | 'range' | 'multiple'
  selectionHistory: SelectionHistoryEntry[]
}

interface SelectionHistoryEntry {
  timestamp: number
  action: 'add' | 'remove' | 'clear' | 'range'
  tabIds: string[]
}
```

#### 跨窗口组选择
- 支持在不同窗口组之间维持选择状态
- 切换窗口组时保留已选择的标签页
- 提供跨组选择统计信息

#### 选择计数显示
- 实时显示选择的标签页数量
- 在不同视图中显示选择状态
- 支持选择预览功能

#### 选择状态持久化
```javascript
interface SelectionPersistence {
  saveSelection(name: string): void
  loadSelection(name: string): void
  getSavedSelections(): SavedSelection[]
  deleteSelection(name: string): void
}
```

## 3. 批量操作功能

### 3.1 批量打开

#### 功能描述
支持将选中的标签页批量打开，提供多种打开方式和限流控制。

#### 实现细节
```javascript
interface BatchOpenOptions {
  target: 'newWindow' | 'currentWindow' | 'backgroundWindow'
  grouping: 'none' | 'byDomain' | 'byWindow'
  limit: number // 每批次打开的最大数量
  delay: number // 批次之间的延迟（毫秒）
  openInactive: boolean // 是否在后台打开
}

class BatchOpenHandler {
  async performBatchOpen(
    tabIds: string[], 
    options: BatchOpenOptions
  ): Promise<BatchOpenResult> {
    const batches = this.createBatches(tabIds, options.limit)
    const results: OpenResult[] = []
    
    for (const batch of batches) {
      const batchResult = await this.openBatch(batch, options)
      results.push(batchResult)
      
      if (options.delay > 0) {
        await this.sleep(options.delay)
      }
    }
    
    return this.aggregateResults(results)
  }
}
```

### 3.2 批量删除

#### 功能描述
安全地删除多个标签页，提供确认和撤销机制。

#### 实现细节
```javascript
interface BatchDeleteOptions {
  confirmThreshold: number // 需要确认的最小数量
  preservePinned: boolean // 是否保留固定标签页
  createBackup: boolean // 是否创建备份
}

class BatchDeleteHandler {
  async performBatchDelete(
    tabIds: string[],
    options: BatchDeleteOptions
  ): Promise<BatchDeleteResult> {
    // 安全检查
    const safeToDelete = await this.validateDeletion(tabIds, options)
    
    if (safeToDelete.length >= options.confirmThreshold) {
      const confirmed = await this.showConfirmDialog({
        count: safeToDelete.length,
        preview: safeToDelete.slice(0, 5)
      })
      
      if (!confirmed) {
        return { cancelled: true }
      }
    }
    
    // 创建备份
    if (options.createBackup) {
      await this.createDeletionBackup(safeToDelete)
    }
    
    // 执行删除
    return await this.executeDeletion(safeToDelete)
  }
}
```

### 3.3 批量移动

#### 功能描述
将选中的标签页移动到指定窗口或创建新窗口。

#### 实现细节
```javascript
interface BatchMoveOptions {
  targetWindow: string | 'new' | 'select'
  position: 'start' | 'end' | 'after-active'
  groupTogether: boolean
  maintainOrder: boolean
}

class BatchMoveHandler {
  async performBatchMove(
    tabIds: string[],
    options: BatchMoveOptions
  ): Promise<BatchMoveResult> {
    const targetWindowId = await this.resolveTargetWindow(options)
    
    // 计算移动顺序
    const moveOperations = this.planMoveOperations(
      tabIds, 
      targetWindowId, 
      options
    )
    
    // 执行移动
    const results = []
    for (const operation of moveOperations) {
      const result = await this.executeMoveOperation(operation)
      results.push(result)
    }
    
    return this.aggregateMoveResults(results)
  }
}
```

### 3.4 批量编辑

#### 功能描述
批量修改标签页的属性，如标签、备注等。

#### 实现细节
```javascript
interface BatchEditOptions {
  updates: {
    tags?: {
      add?: string[]
      remove?: string[]
      set?: string[]
    }
    notes?: {
      append?: string
      prepend?: string
      set?: string
    }
    customData?: Record<string, any>
  }
  skipErrors: boolean
}

class BatchEditHandler {
  async performBatchUpdate(
    tabIds: string[],
    options: BatchEditOptions
  ): Promise<BatchEditResult> {
    const updatePlan = this.createUpdatePlan(tabIds, options.updates)
    const results = []
    
    for (const update of updatePlan) {
      try {
        const result = await this.applyUpdate(update)
        results.push(result)
      } catch (error) {
        if (!options.skipErrors) {
          throw error
        }
        results.push({ 
          tabId: update.tabId, 
          success: false, 
          error 
        })
      }
    }
    
    return this.aggregateEditResults(results)
  }
}
```

## 4. 操作API设计

### 4.1 主要接口定义

```javascript
interface BatchOperation {
  // 批量打开
  performBatchOpen(
    tabIds: string[], 
    options: BatchOpenOptions
  ): Promise<BatchOperationResult>
  
  // 批量删除
  performBatchDelete(
    tabIds: string[]
  ): Promise<BatchOperationResult>
  
  // 批量移动
  performBatchMove(
    tabIds: string[], 
    targetWindowId: string
  ): Promise<BatchOperationResult>
  
  // 批量更新
  performBatchUpdate(
    tabIds: string[], 
    updates: TabUpdates
  ): Promise<BatchOperationResult>
  
  // 取消正在进行的操作
  cancelOperation(operationId: string): Promise<void>
}

interface BatchOperationResult {
  operationId: string
  success: boolean
  totalCount: number
  successCount: number
  failureCount: number
  errors: OperationError[]
  duration: number
  undoToken?: string
}

interface OperationError {
  tabId: string
  error: string
  code: string
  recoverable: boolean
}
```

### 4.2 操作管理器

```javascript
class BatchOperationManager {
  private operations: Map<string, OngoingOperation>
  private operationQueue: OperationQueue
  
  async execute(
    operation: BatchOperation,
    options: ExecutionOptions
  ): Promise<BatchOperationResult> {
    const operationId = this.generateOperationId()
    
    // 创建操作上下文
    const context = new OperationContext({
      operationId,
      operation,
      options,
      startTime: Date.now()
    })
    
    // 添加到队列
    await this.operationQueue.enqueue(context)
    
    // 执行操作
    try {
      const result = await this.executeOperation(context)
      await this.recordOperation(context, result)
      return result
    } catch (error) {
      await this.handleOperationError(context, error)
      throw error
    } finally {
      this.operations.delete(operationId)
    }
  }
}
```

## 5. 进度反馈

### 5.1 进度跟踪

```javascript
interface ProgressTracker {
  operationId: string
  totalItems: number
  processedItems: number
  successItems: number
  failedItems: number
  currentItem?: string
  estimatedTimeRemaining: number
  
  // 进度更新方法
  updateProgress(update: ProgressUpdate): void
  onProgress(callback: ProgressCallback): void
  getProgress(): ProgressSnapshot
}

interface ProgressUpdate {
  processedDelta?: number
  successDelta?: number
  failedDelta?: number
  currentItem?: string
}
```

### 5.2 UI反馈组件

```javascript
interface ProgressUI {
  // 进度条显示
  showProgressBar(options: {
    title: string
    totalItems: number
    cancellable: boolean
  }): ProgressBarHandle
  
  // 详细进度面板
  showProgressPanel(options: {
    operationId: string
    showDetails: boolean
    autoClose: boolean
  }): ProgressPanelHandle
  
  // 通知
  showCompletionNotification(result: BatchOperationResult): void
}
```

### 5.3 错误收集和报告

```javascript
interface ErrorCollector {
  collectError(error: OperationError): void
  getErrors(): OperationError[]
  generateErrorReport(): ErrorReport
  hasRecoverableErrors(): boolean
  suggestRecoveryActions(): RecoveryAction[]
}

interface ErrorReport {
  summary: string
  errors: Array<{
    category: string
    count: number
    samples: OperationError[]
  }>
  recommendations: string[]
}
```

## 6. 撤销/重做机制

### 6.1 操作历史管理

```javascript
interface OperationHistory {
  maxHistorySize: number
  history: HistoryEntry[]
  currentIndex: number
  
  // 添加操作到历史
  addOperation(operation: ReversibleOperation): void
  
  // 撤销/重做
  canUndo(): boolean
  canRedo(): boolean
  undo(): Promise<UndoResult>
  redo(): Promise<RedoResult>
  
  // 历史管理
  clearHistory(): void
  getHistory(): HistoryEntry[]
  getUndoableOperations(): ReversibleOperation[]
}

interface ReversibleOperation {
  id: string
  type: string
  timestamp: number
  description: string
  
  // 执行撤销
  undo(): Promise<void>
  
  // 执行重做
  redo(): Promise<void>
  
  // 是否仍然有效
  isValid(): boolean
}
```

### 6.2 撤销操作实现

```javascript
class UndoableOperation implements ReversibleOperation {
  constructor(
    private originalState: State,
    private newState: State,
    private operations: Operation[]
  ) {}
  
  async undo(): Promise<void> {
    // 验证操作是否可撤销
    if (!this.canUndo()) {
      throw new Error('Operation cannot be undone')
    }
    
    // 恢复原始状态
    await this.restoreState(this.originalState)
    
    // 触发撤销事件
    this.emitUndoEvent()
  }
  
  async redo(): Promise<void> {
    // 重新执行操作
    for (const op of this.operations) {
      await op.execute()
    }
    
    // 触发重做事件
    this.emitRedoEvent()
  }
  
  private canUndo(): boolean {
    // 检查相关资源是否仍然存在
    // 检查是否有冲突的操作
    return true
  }
}
```

### 6.3 撤销栈配置

```javascript
interface UndoStackConfig {
  maxStackSize: number // 默认 50
  groupingTimeout: number // 操作分组超时（毫秒）
  persistToStorage: boolean // 是否持久化撤销历史
  compressHistory: boolean // 是否压缩历史记录
}
```

## 7. 安全机制

### 7.1 操作确认

```javascript
interface ConfirmationPolicy {
  // 需要确认的条件
  requiresConfirmation(operation: BatchOperation): boolean
  
  // 显示确认对话框
  showConfirmation(options: {
    operation: string
    itemCount: number
    preview: any[]
    risks: string[]
  }): Promise<boolean>
  
  // 记住用户选择
  rememberChoice(operation: string, choice: boolean): void
}

class DefaultConfirmationPolicy implements ConfirmationPolicy {
  requiresConfirmation(operation: BatchOperation): boolean {
    // 删除操作总是需要确认
    if (operation.type === 'delete') {
      return true
    }
    
    // 大批量操作需要确认
    if (operation.itemCount > 20) {
      return true
    }
    
    // 涉及固定标签页的操作需要确认
    if (operation.affectsPinnedTabs) {
      return true
    }
    
    return false
  }
}
```

### 7.2 危险操作警告

```javascript
interface RiskAssessment {
  assessRisk(operation: BatchOperation): RiskLevel
  
  getRiskFactors(operation: BatchOperation): RiskFactor[]
  
  showRiskWarning(
    riskLevel: RiskLevel, 
    factors: RiskFactor[]
  ): Promise<boolean>
}

enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

interface RiskFactor {
  type: string
  description: string
  severity: number
  mitigation?: string
}
```

### 7.3 操作限制

```javascript
interface OperationLimits {
  maxBatchSize: number // 单次操作的最大项目数
  maxConcurrentOperations: number // 最大并发操作数
  rateLimiting: {
    maxOperationsPerMinute: number
    maxItemsPerMinute: number
  }
  
  // 检查是否超出限制
  checkLimits(operation: BatchOperation): LimitCheckResult
  
  // 应用限制
  applyLimits(operation: BatchOperation): BatchOperation
}

interface LimitCheckResult {
  allowed: boolean
  reason?: string
  suggestion?: string
}
```

### 7.4 权限检查

```javascript
interface PermissionChecker {
  // 检查操作权限
  checkPermission(
    operation: BatchOperation,
    context: SecurityContext
  ): Promise<PermissionResult>
  
  // 请求必要权限
  requestPermissions(
    permissions: string[]
  ): Promise<boolean>
  
  // 获取当前权限
  getCurrentPermissions(): string[]
}

interface SecurityContext {
  userId: string
  userRole: string
  sessionId: string
  origin: string
}
```

## 8. UI交互设计

### 8.1 选择模式UI

```javascript
interface SelectionModeUI {
  // 进入/退出选择模式
  enterSelectionMode(): void
  exitSelectionMode(): void
  
  // 显示选择工具栏
  showSelectionToolbar(options: {
    selectedCount: number
    availableActions: string[]
  }): void
  
  // 选择指示器
  highlightSelected(tabIds: string[]): void
  showSelectionCount(count: number): void
}
```

### 8.2 批量操作菜单

```javascript
interface BatchOperationMenu {
  // 上下文菜单
  showContextMenu(options: {
    selectedItems: string[]
    position: Position
  }): void
  
  // 操作工具栏
  renderToolbar(actions: BatchAction[]): void
  
  // 快速操作按钮
  showQuickActions(selectedCount: number): void
}
```

### 8.3 进度和反馈UI

```javascript
interface FeedbackUI {
  // 操作进度
  showProgress(progress: Progress): void
  
  // 成功/失败提示
  showResult(result: BatchOperationResult): void
  
  // 错误详情
  showErrorDetails(errors: OperationError[]): void
  
  // 撤销提示
  showUndoToast(operation: ReversibleOperation): void
}
```

## 9. 错误处理

### 9.1 错误类型

```javascript
enum BatchOperationErrorType {
  PERMISSION_DENIED = 'permission_denied',
  RESOURCE_NOT_FOUND = 'resource_not_found',
  OPERATION_CANCELLED = 'operation_cancelled',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  NETWORK_ERROR = 'network_error',
  VALIDATION_ERROR = 'validation_error',
  PARTIAL_FAILURE = 'partial_failure'
}
```

### 9.2 错误恢复策略

```javascript
interface ErrorRecoveryStrategy {
  // 判断是否可恢复
  isRecoverable(error: OperationError): boolean
  
  // 获取恢复选项
  getRecoveryOptions(error: OperationError): RecoveryOption[]
  
  // 执行恢复
  attemptRecovery(
    error: OperationError,
    option: RecoveryOption
  ): Promise<RecoveryResult>
}

interface RecoveryOption {
  id: string
  label: string
  description: string
  automatic: boolean
}
```

### 9.3 部分失败处理

```javascript
class PartialFailureHandler {
  handlePartialFailure(
    result: BatchOperationResult
  ): PartialFailureResolution {
    const failed = result.errors
    const succeeded = result.successCount
    
    return {
      summary: `${succeeded} 项成功，${failed.length} 项失败`,
      failedItems: failed.map(e => e.tabId),
      recoveryActions: this.suggestRecoveryActions(failed),
      canRetry: this.canRetryFailed(failed),
      canContinue: true
    }
  }
  
  async retryFailed(
    errors: OperationError[],
    options: RetryOptions
  ): Promise<BatchOperationResult> {
    const retryableErrors = errors.filter(e => e.recoverable)
    // 实现重试逻辑
  }
}
```

## 10. 性能优化

### 10.1 批处理优化

```javascript
interface BatchProcessor {
  // 智能分批
  createOptimalBatches(
    items: any[],
    constraints: BatchConstraints
  ): any[][]
  
  // 并发控制
  processConcurrently(
    batches: any[][],
    processor: BatchFunction,
    concurrency: number
  ): Promise<any[]>
  
  // 进度流
  processWithProgress(
    items: any[],
    processor: ItemProcessor
  ): AsyncGenerator<ProgressUpdate>
}
```

### 10.2 缓存策略

```javascript
interface BatchOperationCache {
  // 缓存操作结果
  cacheResult(
    operationId: string,
    result: BatchOperationResult
  ): void
  
  // 缓存选择状态
  cacheSelection(
    selectionId: string,
    tabIds: string[]
  ): void
  
  // 清理过期缓存
  cleanupExpired(): void
}
```

## 11. 集成要求

### 11.1 与其他模块的集成

- **标签页管理器**：获取标签页信息，执行实际的标签页操作
- **窗口管理器**：处理窗口相关的批量操作
- **数据存储**：保存操作历史和撤销信息
- **权限系统**：检查和请求必要的操作权限
- **通知系统**：显示操作进度和结果

### 11.2 事件系统

```javascript
interface BatchOperationEvents {
  // 选择事件
  'selection:changed': (selection: SelectionState) => void
  'selection:cleared': () => void
  
  // 操作事件
  'operation:started': (operation: BatchOperation) => void
  'operation:progress': (progress: Progress) => void
  'operation:completed': (result: BatchOperationResult) => void
  'operation:failed': (error: Error) => void
  
  // 撤销事件
  'undo:executed': (operation: ReversibleOperation) => void
  'redo:executed': (operation: ReversibleOperation) => void
}
```

## 12. 测试要求

### 12.1 单元测试

- 选择逻辑测试
- 批量操作算法测试
- 撤销/重做机制测试
- 错误处理测试

### 12.2 集成测试

- 跨模块操作测试
- 并发操作测试
- 性能压力测试
- 边界条件测试

### 12.3 用户体验测试

- 选择交互测试
- 操作反馈测试
- 错误恢复测试
- 性能感知测试