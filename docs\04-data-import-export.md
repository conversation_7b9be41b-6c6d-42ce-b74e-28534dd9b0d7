# 数据导入导出模块需求文档

## 1. 模块概述

数据导入导出模块负责在 MyTab3 扩展与浏览器之间进行数据交换，以及提供完整的数据备份和恢复功能。该模块包含以下核心功能：

- **浏览器数据导入**：从当前浏览器会话导入标签页和窗口数据
- **数据导出**：将扩展中的数据导出为标准格式文件
- **数据导入**：从备份文件恢复数据
- **数据验证**：确保导入数据的完整性和兼容性

## 2. 浏览器数据导入功能

### 2.1 数据获取

使用 Chrome Extension API 获取浏览器数据：

```javascript
// 获取所有标签页
const tabs = await chrome.tabs.query({});

// 获取所有窗口
const windows = await chrome.windows.getAll({ populate: true });
```

### 2.2 数据转换逻辑

#### 浏览器 Tab 对象转换为内部 Tab 模型

```javascript
// 浏览器 Tab 对象结构
{
  id: number,
  windowId: number,
  index: number,
  url: string,
  title: string,
  favIconUrl?: string,
  active: boolean,
  pinned: boolean,
  audible: boolean,
  mutedInfo?: {
    muted: boolean,
    reason?: string
  },
  status?: 'loading' | 'complete',
  incognito: boolean,
  selected: boolean,
  highlighted: boolean,
  groupId: number
}

// 转换为内部 Tab 模型
{
  id: string,                    // 生成新的UUID
  browserTabId: number,          // 保存原始tab.id
  windowId: string,              // 转换后的窗口ID
  title: string,
  url: string,
  favicon: string,
  isPinned: boolean,
  isActive: boolean,
  isAudible: boolean,
  isMuted: boolean,
  groupId?: string,              // 转换后的分组ID
  index: number,
  createdAt: Date,
  updatedAt: Date,
  lastAccessTime: Date,
  metadata: {
    status: string,
    incognito: boolean,
    highlighted: boolean
  }
}
```

#### 浏览器 Window 对象转换为内部 Window 模型

```javascript
// 浏览器 Window 对象结构
{
  id: number,
  focused: boolean,
  top: number,
  left: number,
  width: number,
  height: number,
  incognito: boolean,
  type: 'normal' | 'popup' | 'panel' | 'app' | 'devtools',
  state: 'normal' | 'minimized' | 'maximized' | 'fullscreen' | 'locked-fullscreen',
  alwaysOnTop: boolean,
  tabs?: Tab[]
}

// 转换为内部 Window 模型
{
  id: string,                    // 生成新的UUID
  browserWindowId: number,       // 保存原始window.id
  name: string,                  // 自动生成或用户指定
  isActive: boolean,
  position: {
    top: number,
    left: number,
    width: number,
    height: number
  },
  state: string,
  type: string,
  createdAt: Date,
  updatedAt: Date,
  tabCount: number,
  metadata: {
    incognito: boolean,
    alwaysOnTop: boolean
  }
}
```

### 2.3 导入选项

```typescript
interface ImportFromBrowserOptions {
  scope: 'all' | 'current' | 'selected';  // 导入范围
  windowIds?: number[];                    // 选择性导入的窗口ID列表
  includeIncognito: boolean;               // 是否包含隐身窗口
  groupTabs: boolean;                      // 是否保留标签分组
  generateNames: boolean;                  // 是否自动生成窗口名称
  mergeStrategy: 'create' | 'merge';       // 创建新窗口或合并到现有窗口
}
```

## 3. 数据导出功能

### 3.1 导出格式定义

```json
{
  "version": "1.0",
  "exportDate": "2024-01-01T00:00:00Z",
  "application": {
    "name": "MyTab3",
    "version": "3.0.0"
  },
  "windows": [
    {
      "id": "uuid-window-1",
      "name": "工作窗口",
      "isActive": true,
      "position": {
        "top": 100,
        "left": 100,
        "width": 1200,
        "height": 800
      },
      "state": "normal",
      "type": "normal",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "tabCount": 5,
      "metadata": {
        "incognito": false,
        "alwaysOnTop": false
      }
    }
  ],
  "tabs": [
    {
      "id": "uuid-tab-1",
      "windowId": "uuid-window-1",
      "title": "页面标题",
      "url": "https://example.com",
      "favicon": "https://example.com/favicon.ico",
      "isPinned": false,
      "isActive": true,
      "isAudible": false,
      "isMuted": false,
      "groupId": "uuid-group-1",
      "index": 0,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "lastAccessTime": "2024-01-01T00:00:00Z",
      "metadata": {
        "status": "complete",
        "incognito": false,
        "highlighted": true
      }
    }
  ],
  "groups": [
    {
      "id": "uuid-group-1",
      "windowId": "uuid-window-1",
      "title": "工作相关",
      "color": "#FF0000",
      "collapsed": false,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "settings": {
    "theme": "light",
    "autoSave": true,
    "saveInterval": 300000,
    "shortcuts": {
      "saveWindow": "Ctrl+Shift+S",
      "restoreWindow": "Ctrl+Shift+R"
    }
  }
}
```

### 3.2 导出选项

```typescript
interface ExportOptions {
  includeWindows: string[] | 'all';        // 要导出的窗口ID列表
  includeTabs: boolean;                    // 是否包含标签页
  includeGroups: boolean;                  // 是否包含分组信息
  includeSettings: boolean;                // 是否包含设置
  includeFavicons: boolean;                // 是否包含图标数据
  format: 'json' | 'compressed';           // 导出格式
  encryption?: {                           // 加密选项
    enabled: boolean;
    password?: string;
  };
}
```

## 4. 数据导入功能

### 4.1 文件验证

```typescript
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  fileInfo: {
    version: string;
    exportDate: Date;
    windowCount: number;
    tabCount: number;
    hasSettings: boolean;
  };
}

interface ValidationError {
  code: string;
  message: string;
  field?: string;
}

interface ValidationWarning {
  code: string;
  message: string;
  field?: string;
}

// 验证规则
const validationRules = {
  version: /^[0-9]+\.[0-9]+$/,
  requiredFields: ['version', 'exportDate', 'windows', 'tabs'],
  maxFileSize: 50 * 1024 * 1024,  // 50MB
  supportedVersions: ['1.0', '1.1']
};
```

### 4.2 冲突处理策略

```typescript
interface ImportOptions {
  conflictStrategy: 'overwrite' | 'merge' | 'duplicate';
  mergeRules?: {
    windows: 'replace' | 'merge' | 'skip';
    tabs: 'replace' | 'merge' | 'skip';
    settings: 'replace' | 'merge' | 'skip';
  };
  duplicateNameSuffix?: string;  // 默认: " (导入)"
  preserveIds: boolean;           // 是否保留原始ID
}

// 冲突检测
interface ConflictInfo {
  type: 'window' | 'tab' | 'group';
  existingItem: any;
  importItem: any;
  resolution: 'overwrite' | 'merge' | 'skip' | 'duplicate';
}
```

### 4.3 导入进度反馈

```typescript
interface ImportProgress {
  phase: 'validating' | 'preparing' | 'importing' | 'finalizing';
  current: number;
  total: number;
  currentItem?: string;
  errors: ImportError[];
}

interface ImportResult {
  success: boolean;
  imported: {
    windows: number;
    tabs: number;
    groups: number;
    settings: boolean;
  };
  skipped: {
    windows: number;
    tabs: number;
    groups: number;
  };
  errors: ImportError[];
  duration: number;  // 毫秒
}
```

## 5. API 接口定义

### 5.1 从浏览器导入

```typescript
async function importFromBrowser(
  options: ImportFromBrowserOptions
): Promise<ImportResult> {
  // 实现步骤：
  // 1. 根据选项获取浏览器数据
  // 2. 转换数据格式
  // 3. 验证数据完整性
  // 4. 保存到存储
  // 5. 返回导入结果
}
```

### 5.2 导出数据

```typescript
async function exportData(
  options: ExportOptions
): Promise<Blob> {
  // 实现步骤：
  // 1. 根据选项收集数据
  // 2. 构建导出对象
  // 3. 验证导出数据
  // 4. 序列化为JSON
  // 5. 可选：压缩/加密
  // 6. 返回Blob对象
}
```

### 5.3 导入数据

```typescript
async function importData(
  file: File,
  options: ImportOptions
): Promise<ImportResult> {
  // 实现步骤：
  // 1. 读取文件内容
  // 2. 验证文件格式
  // 3. 解析数据
  // 4. 检测冲突
  // 5. 应用冲突策略
  // 6. 导入数据
  // 7. 返回导入结果
}
```

### 5.4 验证导入文件

```typescript
async function validateImportFile(
  file: File
): Promise<ValidationResult> {
  // 实现步骤：
  // 1. 检查文件大小
  // 2. 读取文件内容
  // 3. 验证JSON格式
  // 4. 检查必需字段
  // 5. 验证版本兼容性
  // 6. 验证数据结构
  // 7. 返回验证结果
}
```

## 6. 错误处理

### 6.1 权限不足处理

```typescript
class PermissionError extends Error {
  constructor(
    public permission: string,
    public action: string
  ) {
    super(`权限不足：需要 ${permission} 权限才能执行 ${action}`);
  }
}

// 权限检查
async function checkPermissions(): Promise<void> {
  const permissions = await chrome.permissions.getAll();
  const required = ['tabs', 'storage', 'downloads'];
  
  const missing = required.filter(p => !permissions.permissions?.includes(p));
  if (missing.length > 0) {
    throw new PermissionError(missing.join(', '), '数据导入导出');
  }
}
```

### 6.2 数据格式错误处理

```typescript
class DataFormatError extends Error {
  constructor(
    public field: string,
    public expectedType: string,
    public actualValue: any
  ) {
    super(`数据格式错误：字段 ${field} 期望类型 ${expectedType}，实际值 ${actualValue}`);
  }
}

// 数据验证
function validateDataStructure(data: any): void {
  if (!data.version || typeof data.version !== 'string') {
    throw new DataFormatError('version', 'string', data.version);
  }
  
  if (!Array.isArray(data.windows)) {
    throw new DataFormatError('windows', 'array', data.windows);
  }
  
  // 更多验证...
}
```

### 6.3 导入失败回滚机制

```typescript
class ImportTransaction {
  private backup: any;
  private operations: Array<() => Promise<void>> = [];
  
  async backup(): Promise<void> {
    // 备份当前数据
    this.backup = await storage.getAll();
  }
  
  addOperation(operation: () => Promise<void>): void {
    this.operations.push(operation);
  }
  
  async commit(): Promise<void> {
    try {
      for (const operation of this.operations) {
        await operation();
      }
    } catch (error) {
      await this.rollback();
      throw error;
    }
  }
  
  async rollback(): Promise<void> {
    // 恢复备份数据
    if (this.backup) {
      await storage.clear();
      await storage.setAll(this.backup);
    }
  }
}

// 使用事务
async function importWithTransaction(
  data: any,
  options: ImportOptions
): Promise<ImportResult> {
  const transaction = new ImportTransaction();
  
  try {
    await transaction.backup();
    
    // 添加导入操作
    transaction.addOperation(async () => {
      await importWindows(data.windows);
    });
    
    transaction.addOperation(async () => {
      await importTabs(data.tabs);
    });
    
    await transaction.commit();
    
    return {
      success: true,
      // ... 其他结果
    };
  } catch (error) {
    return {
      success: false,
      errors: [error as ImportError],
      // ... 其他结果
    };
  }
}
```

## 7. 性能优化建议

### 7.1 批量操作

- 使用批量API减少存储操作次数
- 实现数据分页加载，避免一次性加载大量数据
- 使用 Web Workers 处理大文件解析

### 7.2 内存管理

- 实现流式文件读取，避免大文件占用过多内存
- 及时释放不再使用的对象引用
- 使用 WeakMap 存储临时数据

### 7.3 用户体验

- 提供实时进度反馈
- 支持导入/导出操作的取消
- 实现增量导入，允许用户选择性导入部分数据