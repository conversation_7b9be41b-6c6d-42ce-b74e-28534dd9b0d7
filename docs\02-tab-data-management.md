# 标签页数据管理模块需求文档

## 1. 模块概述

### 1.1 模块职责和功能范围

标签页数据管理模块是MyTab3系统的核心数据管理组件，负责：

- **数据模型管理**：定义和维护标签页数据结构
- **CRUD操作**：提供标签页数据的创建、读取、更新、删除功能
- **批量操作**：支持批量导入、更新和删除标签页
- **数据验证**：确保数据完整性和有效性
- **查询优化**：提供高效的数据检索和过滤功能
- **事件通知**：数据变更时触发相应事件

### 1.2 与其他模块的关系

```
┌─────────────────────┐
│    窗口管理模块      │
│  (Window Manager)   │
└──────────┬──────────┘
           │ 窗口-标签页关联
           ▼
┌─────────────────────┐
│  标签页数据管理模块   │ ◄─── 本模块
│   (Tab Manager)     │
└──────────┬──────────┘
           │ 数据持久化
           ▼
┌─────────────────────┐
│    持久化模块        │
│ (Storage Manager)   │
└─────────────────────┘
```

## 2. 数据模型详细定义

### 2.1 Tab数据结构

```javascript
interface Tab {
  // 基础标识
  id: string;           // UUID格式，唯一标识符
  windowId: string;     // 所属窗口ID
  
  // 基本信息
  url: string;          // 标签页URL
  title: string;        // 标签页标题
  favIconUrl: string;   // 网站图标URL
  index: number;        // 在窗口中的位置索引
  
  // 时间戳
  createdAt: number;    // 创建时间（Unix时间戳）
  updatedAt: number;    // 最后更新时间
  lastAccessedAt: number; // 最后访问时间
  
  // 扩展属性
  tags: string[];       // 标签数组
  note: string;         // 用户备注
  isPinned: boolean;    // 是否固定
  groupId?: string;     // 分组ID（可选）
}
```

### 2.2 数据字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 是 | UUID v4格式，系统自动生成 |
| windowId | string | 是 | 关联的窗口ID |
| url | string | 是 | 完整的URL地址，包含协议 |
| title | string | 是 | 页面标题，默认为URL |
| favIconUrl | string | 否 | 网站图标URL，可为空字符串 |
| index | number | 是 | 非负整数，表示在窗口中的位置 |
| createdAt | number | 是 | 创建时的Unix时间戳 |
| updatedAt | number | 是 | 最后更新时的Unix时间戳 |
| lastAccessedAt | number | 是 | 最后访问时的Unix时间戳 |
| tags | string[] | 是 | 标签数组，可为空数组 |
| note | string | 是 | 用户备注，可为空字符串 |
| isPinned | boolean | 是 | 是否固定，默认false |
| groupId | string | 否 | 分组ID，用于标签页分组功能 |

## 3. API接口定义

### 3.1 创建标签页 - createTab

**功能描述**：创建单个标签页记录

**接口定义**：
```typescript
createTab(tabData: Partial<Tab>): Promise<Tab>
```

**参数说明**：
- `tabData`: 标签页数据对象，必须包含必填字段

**返回值**：
- 成功：返回创建的完整Tab对象
- 失败：抛出错误

**错误处理**：
- `ValidationError`: 数据验证失败
- `DuplicateError`: ID重复
- `StorageError`: 存储失败

**示例**：
```javascript
const newTab = await tabManager.createTab({
  url: 'https://example.com',
  title: 'Example Site',
  windowId: 'window-123',
  index: 0
});
```

### 3.2 更新标签页 - updateTab

**功能描述**：更新指定标签页的信息

**接口定义**：
```typescript
updateTab(id: string, updates: Partial<Tab>): Promise<Tab>
```

**参数说明**：
- `id`: 标签页ID
- `updates`: 要更新的字段对象

**返回值**：
- 成功：返回更新后的完整Tab对象
- 失败：抛出错误

**错误处理**：
- `NotFoundError`: 标签页不存在
- `ValidationError`: 更新数据无效
- `StorageError`: 存储失败

**示例**：
```javascript
const updatedTab = await tabManager.updateTab('tab-123', {
  title: 'New Title',
  tags: ['work', 'important'],
  isPinned: true
});
```

### 3.3 删除标签页 - deleteTab

**功能描述**：删除指定的标签页

**接口定义**：
```typescript
deleteTab(id: string): Promise<boolean>
```

**参数说明**：
- `id`: 要删除的标签页ID

**返回值**：
- 成功：返回true
- 失败：抛出错误

**错误处理**：
- `NotFoundError`: 标签页不存在
- `StorageError`: 删除失败

### 3.4 获取单个标签页 - getTab

**功能描述**：获取指定ID的标签页信息

**接口定义**：
```typescript
getTab(id: string): Promise<Tab | null>
```

**参数说明**：
- `id`: 标签页ID

**返回值**：
- 成功：返回Tab对象或null（不存在时）
- 失败：抛出错误

### 3.5 查询标签页列表 - getTabs

**功能描述**：根据过滤条件查询标签页列表

**接口定义**：
```typescript
interface TabFilter {
  windowId?: string;
  tags?: string[];
  isPinned?: boolean;
  groupId?: string;
  searchText?: string;
  createdAfter?: number;
  createdBefore?: number;
  limit?: number;
  offset?: number;
  orderBy?: 'createdAt' | 'updatedAt' | 'lastAccessedAt' | 'index';
  order?: 'asc' | 'desc';
}

getTabs(filter?: TabFilter): Promise<Tab[]>
```

**参数说明**：
- `filter`: 过滤条件对象（可选）

**返回值**：
- 成功：返回符合条件的Tab数组
- 失败：抛出错误

**示例**：
```javascript
// 获取特定窗口的所有固定标签页
const pinnedTabs = await tabManager.getTabs({
  windowId: 'window-123',
  isPinned: true,
  orderBy: 'index',
  order: 'asc'
});
```

### 3.6 批量创建标签页 - batchCreateTabs

**功能描述**：批量创建多个标签页

**接口定义**：
```typescript
batchCreateTabs(tabs: Partial<Tab>[]): Promise<Tab[]>
```

**参数说明**：
- `tabs`: 标签页数据数组

**返回值**：
- 成功：返回创建的Tab对象数组
- 失败：抛出错误（事务回滚）

**错误处理**：
- `ValidationError`: 任一数据验证失败
- `StorageError`: 存储失败

### 3.7 批量更新标签页 - batchUpdateTabs

**功能描述**：批量更新多个标签页

**接口定义**：
```typescript
interface TabUpdate {
  id: string;
  updates: Partial<Tab>;
}

batchUpdateTabs(updates: TabUpdate[]): Promise<Tab[]>
```

**参数说明**：
- `updates`: 更新操作数组

**返回值**：
- 成功：返回更新后的Tab对象数组
- 失败：抛出错误（事务回滚）

### 3.8 批量删除标签页 - batchDeleteTabs

**功能描述**：批量删除多个标签页

**接口定义**：
```typescript
batchDeleteTabs(ids: string[]): Promise<boolean>
```

**参数说明**：
- `ids`: 要删除的标签页ID数组

**返回值**：
- 成功：返回true
- 失败：抛出错误（事务回滚）

## 4. 数据验证规则

### 4.1 URL格式验证

```javascript
function validateUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    // 支持的协议
    const allowedProtocols = ['http:', 'https:', 'file:', 'chrome:', 'chrome-extension:'];
    return allowedProtocols.includes(urlObj.protocol);
  } catch {
    return false;
  }
}
```

### 4.2 必填字段检查

```javascript
const requiredFields = ['url', 'windowId', 'index'];

function validateRequiredFields(tabData: Partial<Tab>): void {
  for (const field of requiredFields) {
    if (tabData[field] === undefined || tabData[field] === null) {
      throw new ValidationError(`Missing required field: ${field}`);
    }
  }
}
```

### 4.3 数据类型验证

```javascript
const fieldValidators = {
  id: (v) => typeof v === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v),
  windowId: (v) => typeof v === 'string' && v.length > 0,
  url: (v) => typeof v === 'string' && validateUrl(v),
  title: (v) => typeof v === 'string',
  favIconUrl: (v) => typeof v === 'string',
  index: (v) => Number.isInteger(v) && v >= 0,
  createdAt: (v) => Number.isInteger(v) && v > 0,
  updatedAt: (v) => Number.isInteger(v) && v > 0,
  lastAccessedAt: (v) => Number.isInteger(v) && v > 0,
  tags: (v) => Array.isArray(v) && v.every(tag => typeof tag === 'string'),
  note: (v) => typeof v === 'string',
  isPinned: (v) => typeof v === 'boolean',
  groupId: (v) => v === undefined || typeof v === 'string'
};
```

## 5. 索引策略

### 5.1 主键索引
- 字段：`id`
- 类型：唯一索引
- 用途：快速查找单个标签页

### 5.2 窗口索引
- 字段：`windowId`
- 类型：非唯一索引
- 用途：快速查询特定窗口的所有标签页

### 5.3 时间索引
- 字段：`createdAt`, `updatedAt`, `lastAccessedAt`
- 类型：非唯一索引
- 用途：按时间排序和范围查询

### 5.4 全文搜索索引
- 字段：`title`, `url`, `note`
- 类型：全文索引
- 用途：支持关键词搜索功能

### 5.5 复合索引
- 字段：`windowId + index`
- 类型：复合索引
- 用途：快速定位窗口中特定位置的标签页

## 6. 与其他模块的交互

### 6.1 与窗口管理模块协作

**窗口创建时**：
```javascript
// 窗口管理模块通知
windowManager.on('window:created', async (window) => {
  // 标签页管理模块可以预创建空标签页
  if (window.autoCreateTab) {
    await tabManager.createTab({
      windowId: window.id,
      url: 'chrome://newtab',
      title: 'New Tab',
      index: 0
    });
  }
});
```

**窗口删除时**：
```javascript
// 级联删除所有标签页
windowManager.on('window:deleted', async (windowId) => {
  const tabs = await tabManager.getTabs({ windowId });
  await tabManager.batchDeleteTabs(tabs.map(tab => tab.id));
});
```

### 6.2 与持久化模块交互

**数据保存**：
```javascript
class TabManager {
  private storageManager: StorageManager;
  
  async createTab(tabData: Partial<Tab>): Promise<Tab> {
    const tab = this.prepareTabData(tabData);
    
    // 保存到持久化存储
    await this.storageManager.save('tabs', tab.id, tab);
    
    // 更新索引
    await this.storageManager.updateIndex('tabs', 'windowId', tab.windowId, tab.id);
    
    return tab;
  }
}
```

### 6.3 事件触发机制

**支持的事件**：
- `tab:created` - 标签页创建后
- `tab:updated` - 标签页更新后
- `tab:deleted` - 标签页删除后
- `tab:batch-created` - 批量创建后
- `tab:batch-updated` - 批量更新后
- `tab:batch-deleted` - 批量删除后

**事件数据格式**：
```javascript
interface TabEvent {
  type: string;
  timestamp: number;
  data: {
    tab?: Tab;
    tabs?: Tab[];
    updates?: Partial<Tab>;
    id?: string;
    ids?: string[];
  };
}
```

**使用示例**：
```javascript
tabManager.on('tab:updated', (event) => {
  console.log(`Tab ${event.data.tab.id} updated at ${event.timestamp}`);
  
  // 触发UI更新
  uiManager.updateTabDisplay(event.data.tab);
  
  // 记录操作日志
  logger.log('tab_update', event);
});
```

## 7. 性能考虑

### 7.1 批量操作优化
- 使用事务处理批量操作
- 批量更新索引而非逐个更新
- 限制单次批量操作的数量（建议不超过1000条）

### 7.2 查询优化
- 使用索引加速查询
- 实现查询结果缓存
- 支持分页查询避免一次加载过多数据

### 7.3 内存管理
- 实现LRU缓存策略
- 定期清理过期缓存
- 监控内存使用情况

## 8. 错误处理规范

### 8.1 错误类型定义

```javascript
class TabError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'TabError';
  }
}

class ValidationError extends TabError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR');
  }
}

class NotFoundError extends TabError {
  constructor(message: string) {
    super(message, 'NOT_FOUND');
  }
}

class StorageError extends TabError {
  constructor(message: string) {
    super(message, 'STORAGE_ERROR');
  }
}
```

### 8.2 错误处理示例

```javascript
try {
  const tab = await tabManager.updateTab(id, updates);
  return { success: true, data: tab };
} catch (error) {
  if (error instanceof ValidationError) {
    return { success: false, error: 'Invalid data', details: error.message };
  } else if (error instanceof NotFoundError) {
    return { success: false, error: 'Tab not found', details: error.message };
  } else {
    // 记录未知错误
    logger.error('Unexpected error in updateTab', error);
    return { success: false, error: 'Internal error' };
  }
}
```