<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyTab3 功能验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1a73e8;
            border-bottom: 2px solid #e8f0fe;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .test-pass { background: #1e8e3e; }
        .test-fail { background: #d93025; }
        .test-pending { background: #f9ab00; }
        .test-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1557b0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e8f0fe;
        }
        .error {
            background: #fce8e6;
            color: #d93025;
        }
        .success {
            background: #e6f4ea;
            color: #1e8e3e;
        }
    </style>
</head>
<body>
    <h1>MyTab3 插件功能验证测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">📋 测试清单总览</h2>
        <div id="test-summary">
            <div class="test-item">
                <div class="test-status test-pending">?</div>
                <span>总测试项目: <span id="total-tests">0</span></span>
            </div>
            <div class="test-item">
                <div class="test-status test-pass">✓</div>
                <span>通过: <span id="passed-tests">0</span></span>
            </div>
            <div class="test-item">
                <div class="test-status test-fail">✗</div>
                <span>失败: <span id="failed-tests">0</span></span>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔧 核心功能测试</h2>
        
        <div class="test-item">
            <div class="test-status test-pending" id="status-1">?</div>
            <span>测试1: 插件入口机制</span>
            <button class="test-button" onclick="testPluginEntry()">测试</button>
        </div>
        <div id="result-1" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-2">?</div>
            <span>测试2: 本地存储API</span>
            <button class="test-button" onclick="testStorageAPI()">测试</button>
        </div>
        <div id="result-2" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-3">?</div>
            <span>测试3: 窗口和标签页API</span>
            <button class="test-button" onclick="testWindowTabAPI()">测试</button>
        </div>
        <div id="result-3" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-4">?</div>
            <span>测试4: 浏览器数据导入</span>
            <button class="test-button" onclick="testBrowserImport()">测试</button>
        </div>
        <div id="result-4" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-5">?</div>
            <span>测试5: UI组件加载</span>
            <button class="test-button" onclick="testUIComponents()">测试</button>
        </div>
        <div id="result-5" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🎯 用户需求验证</h2>
        
        <div class="test-item">
            <div class="test-status test-pending" id="status-6">?</div>
            <span>需求1: 完整页面而非弹窗</span>
            <button class="test-button" onclick="testFullPageRequirement()">验证</button>
        </div>
        <div id="result-6" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-7">?</div>
            <span>需求2: 书签式标签页管理</span>
            <button class="test-button" onclick="testBookmarkStyleManagement()">验证</button>
        </div>
        <div id="result-7" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-8">?</div>
            <span>需求3: 横向展示布局</span>
            <button class="test-button" onclick="testHorizontalLayout()">验证</button>
        </div>
        <div id="result-8" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-9">?</div>
            <span>需求4: 搜索功能</span>
            <button class="test-button" onclick="testSearchFunction()">验证</button>
        </div>
        <div id="result-9" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <button class="test-button" onclick="runAllTests()" style="font-size: 16px; padding: 12px 24px;">
            🚀 运行所有测试
        </button>
        <button class="test-button" onclick="generateReport()" style="font-size: 16px; padding: 12px 24px;">
            📊 生成测试报告
        </button>
    </div>

    <script>
        let testResults = {};
        let totalTests = 9;

        // 更新测试统计
        function updateTestSummary() {
            document.getElementById('total-tests').textContent = totalTests;
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            document.getElementById('passed-tests').textContent = passed;
            document.getElementById('failed-tests').textContent = failed;
        }

        // 设置测试结果
        function setTestResult(testId, passed, message) {
            testResults[testId] = passed;
            const statusEl = document.getElementById(`status-${testId}`);
            const resultEl = document.getElementById(`result-${testId}`);
            
            if (passed) {
                statusEl.className = 'test-status test-pass';
                statusEl.textContent = '✓';
                resultEl.className = 'test-result success';
            } else {
                statusEl.className = 'test-status test-fail';
                statusEl.textContent = '✗';
                resultEl.className = 'test-result error';
            }
            
            resultEl.textContent = message;
            resultEl.style.display = 'block';
            updateTestSummary();
        }

        // 测试函数
        async function testPluginEntry() {
            try {
                // 检查manifest.json配置
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                if (manifest.action && !manifest.action.default_popup) {
                    setTestResult(1, true, '✅ manifest.json已正确配置，移除了default_popup');
                } else {
                    setTestResult(1, false, '❌ manifest.json仍包含default_popup配置');
                }
            } catch (error) {
                setTestResult(1, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testStorageAPI() {
            try {
                // 测试存储API是否可用
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    setTestResult(2, true, '✅ Chrome存储API可用');
                } else {
                    setTestResult(2, false, '❌ Chrome存储API不可用');
                }
            } catch (error) {
                setTestResult(2, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testWindowTabAPI() {
            try {
                // 检查API文件是否存在
                const windowApiResponse = await fetch('/src/api/window-api.js');
                const tabApiResponse = await fetch('/src/api/tab-api.js');
                
                if (windowApiResponse.ok && tabApiResponse.ok) {
                    setTestResult(3, true, '✅ 窗口和标签页API文件存在');
                } else {
                    setTestResult(3, false, '❌ API文件缺失');
                }
            } catch (error) {
                setTestResult(3, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testBrowserImport() {
            try {
                // 检查background.js是否包含消息处理
                const response = await fetch('/src/background.js');
                const content = await response.text();
                
                if (content.includes('GET_BROWSER_DATA') && content.includes('handleGetBrowserData')) {
                    setTestResult(4, true, '✅ 浏览器数据导入功能已实现');
                } else {
                    setTestResult(4, false, '❌ 浏览器数据导入功能缺失');
                }
            } catch (error) {
                setTestResult(4, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testUIComponents() {
            try {
                // 检查主要UI组件文件
                const components = [
                    '/src/components/window-list.js',
                    '/src/components/tab-card.js',
                    '/src/components/toolbar.js',
                    '/src/pages/management.html',
                    '/src/pages/management.css'
                ];
                
                const results = await Promise.all(
                    components.map(comp => fetch(comp).then(r => r.ok))
                );
                
                if (results.every(r => r)) {
                    setTestResult(5, true, '✅ 所有UI组件文件存在');
                } else {
                    setTestResult(5, false, '❌ 部分UI组件文件缺失');
                }
            } catch (error) {
                setTestResult(5, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testFullPageRequirement() {
            try {
                // 检查background.js中的action点击处理
                const response = await fetch('/src/background.js');
                const content = await response.text();
                
                if (content.includes('chrome.action.onClicked') && content.includes('chrome.tabs.create')) {
                    setTestResult(6, true, '✅ 已配置点击图标打开完整页面');
                } else {
                    setTestResult(6, false, '❌ 未正确配置完整页面打开');
                }
            } catch (error) {
                setTestResult(6, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testBookmarkStyleManagement() {
            try {
                // 检查是否使用本地存储而非直接Chrome API
                const response = await fetch('/src/pages/management.js');
                const content = await response.text();
                
                if (content.includes('getAllWindows') && content.includes('getAllTabs') && 
                    content.includes('import') && !content.includes('chrome.windows.getAll')) {
                    setTestResult(7, true, '✅ 已实现书签式本地存储管理');
                } else {
                    setTestResult(7, false, '❌ 仍在直接使用Chrome API而非本地存储');
                }
            } catch (error) {
                setTestResult(7, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testHorizontalLayout() {
            try {
                // 检查HTML布局结构
                const response = await fetch('/src/pages/management.html');
                const content = await response.text();
                
                if (content.includes('window-sidebar') && content.includes('tabs-content')) {
                    setTestResult(8, true, '✅ 已实现横向布局结构');
                } else {
                    setTestResult(8, false, '❌ 布局结构不符合要求');
                }
            } catch (error) {
                setTestResult(8, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function testSearchFunction() {
            try {
                // 检查搜索功能实现
                const htmlResponse = await fetch('/src/pages/management.html');
                const jsResponse = await fetch('/src/pages/management.js');
                const htmlContent = await htmlResponse.text();
                const jsContent = await jsResponse.text();
                
                if (htmlContent.includes('search-box') && jsContent.includes('handleSearch')) {
                    setTestResult(9, true, '✅ 搜索功能已实现');
                } else {
                    setTestResult(9, false, '❌ 搜索功能缺失');
                }
            } catch (error) {
                setTestResult(9, false, `❌ 测试失败: ${error.message}`);
            }
        }

        async function runAllTests() {
            const tests = [
                testPluginEntry,
                testStorageAPI,
                testWindowTabAPI,
                testBrowserImport,
                testUIComponents,
                testFullPageRequirement,
                testBookmarkStyleManagement,
                testHorizontalLayout,
                testSearchFunction
            ];

            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                // 添加小延迟以便用户看到进度
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        function generateReport() {
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            const total = Object.keys(testResults).length;
            
            const report = `
MyTab3 插件测试报告
==================
测试时间: ${new Date().toLocaleString()}
总测试项: ${total}
通过: ${passed}
失败: ${failed}
通过率: ${total > 0 ? Math.round(passed / total * 100) : 0}%

${total === totalTests && failed === 0 ? '🎉 所有测试通过！插件已准备就绪。' : '⚠️ 仍有测试未通过，需要进一步修复。'}
            `;
            
            alert(report);
        }

        // 初始化
        updateTestSummary();
    </script>
</body>
</html>
