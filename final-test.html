<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1a73e8;
            border-bottom: 2px solid #e8f0fe;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .test-pass { background: #1e8e3e; }
        .test-fail { background: #d93025; }
        .test-pending { background: #f9ab00; }
        .test-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1557b0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e8f0fe;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            background: #fce8e6;
            color: #d93025;
        }
        .success {
            background: #e6f4ea;
            color: #1e8e3e;
        }
        .summary {
            background: #e8f0fe;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
            color: #1a73e8;
        }
    </style>
</head>
<body>
    <h1>🎯 最终功能测试</h1>
    
    <div class="summary">
        <h3>测试说明</h3>
        <p>此测试将验证所有修复是否成功，包括：</p>
        <ul>
            <li>UUID模块导入修复</li>
            <li>消息处理器修复</li>
            <li>浏览器数据导入功能</li>
            <li>页面初始化修复</li>
            <li>主页面功能完整性</li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2 class="test-title">🔧 核心修复验证</h2>
        
        <div class="test-item">
            <div class="test-status test-pending" id="status-1">?</div>
            <span>测试1: UUID模块导入</span>
            <button class="test-button" onclick="testUUIDImport()">测试</button>
        </div>
        <div id="result-1" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-2">?</div>
            <span>测试2: 存储API功能</span>
            <button class="test-button" onclick="testStorageAPI()">测试</button>
        </div>
        <div id="result-2" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-3">?</div>
            <span>测试3: 窗口API功能</span>
            <button class="test-button" onclick="testWindowAPI()">测试</button>
        </div>
        <div id="result-3" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-4">?</div>
            <span>测试4: 标签页API功能</span>
            <button class="test-button" onclick="testTabAPI()">测试</button>
        </div>
        <div id="result-4" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-5">?</div>
            <span>测试5: 消息处理器</span>
            <button class="test-button" onclick="testMessageHandler()">测试</button>
        </div>
        <div id="result-5" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-6">?</div>
            <span>测试6: 浏览器数据导入</span>
            <button class="test-button" onclick="testBrowserImport()">测试</button>
        </div>
        <div id="result-6" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🎯 功能集成测试</h2>
        
        <div class="test-item">
            <div class="test-status test-pending" id="status-7">?</div>
            <span>测试7: 创建测试数据</span>
            <button class="test-button" onclick="testCreateData()">测试</button>
        </div>
        <div id="result-7" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-8">?</div>
            <span>测试8: 数据导入导出</span>
            <button class="test-button" onclick="testImportExport()">测试</button>
        </div>
        <div id="result-8" class="test-result" style="display:none;"></div>

        <div class="test-item">
            <div class="test-status test-pending" id="status-9">?</div>
            <span>测试9: 主页面加载</span>
            <button class="test-button" onclick="testMainPageLoad()">测试</button>
        </div>
        <div id="result-9" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <button class="test-button" onclick="runAllTests()" style="font-size: 16px; padding: 12px 24px;">
            🚀 运行所有测试
        </button>
        <button class="test-button" onclick="openMainPage()" style="font-size: 16px; padding: 12px 24px;">
            📱 打开主页面
        </button>
        <button class="test-button" onclick="generateReport()" style="font-size: 16px; padding: 12px 24px;">
            📊 生成报告
        </button>
    </div>

    <script type="module">
        let testResults = {};
        let totalTests = 9;

        // 设置测试结果
        function setTestResult(testId, passed, message) {
            testResults[testId] = passed;
            const statusEl = document.getElementById(`status-${testId}`);
            const resultEl = document.getElementById(`result-${testId}`);
            
            if (passed) {
                statusEl.className = 'test-status test-pass';
                statusEl.textContent = '✓';
                resultEl.className = 'test-result success';
            } else {
                statusEl.className = 'test-status test-fail';
                statusEl.textContent = '✗';
                resultEl.className = 'test-result error';
            }
            
            resultEl.textContent = message;
            resultEl.style.display = 'block';
        }

        // 测试UUID模块导入
        window.testUUIDImport = async function() {
            try {
                const { v4, isValid } = await import('/src/utils/uuid.js');
                const uuid = v4();
                const isValidUuid = isValid(uuid);
                
                if (uuid && uuid.length === 36 && isValidUuid) {
                    setTestResult(1, true, `✅ UUID模块导入成功\n生成的UUID: ${uuid}\n验证结果: ${isValidUuid}`);
                } else {
                    setTestResult(1, false, `❌ UUID功能异常\nUUID: ${uuid}\n验证: ${isValidUuid}`);
                }
            } catch (error) {
                setTestResult(1, false, `❌ UUID模块导入失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试存储API
        window.testStorageAPI = async function() {
            try {
                const { getStorage, setStorage } = await import('/src/utils/storage.js');
                const testKey = 'test_key_' + Date.now();
                const testData = { test: true, timestamp: Date.now() };
                
                await setStorage(testKey, testData);
                const retrieved = await getStorage(testKey);
                
                if (JSON.stringify(retrieved) === JSON.stringify(testData)) {
                    setTestResult(2, true, `✅ 存储API功能正常\n存储数据: ${JSON.stringify(testData)}\n读取数据: ${JSON.stringify(retrieved)}`);
                } else {
                    setTestResult(2, false, `❌ 存储数据不匹配\n存储: ${JSON.stringify(testData)}\n读取: ${JSON.stringify(retrieved)}`);
                }
            } catch (error) {
                setTestResult(2, false, `❌ 存储API测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试窗口API
        window.testWindowAPI = async function() {
            try {
                const { getAllWindows, createWindow } = await import('/src/api/window-api.js');
                
                // 测试获取所有窗口
                const windows = await getAllWindows();
                
                // 测试创建窗口
                const newWindow = await createWindow({
                    name: '测试窗口',
                    color: '#ff0000'
                });
                
                setTestResult(3, true, `✅ 窗口API功能正常\n现有窗口数: ${windows.length}\n新建窗口ID: ${newWindow.windowId}\n新建窗口名: ${newWindow.name}`);
            } catch (error) {
                setTestResult(3, false, `❌ 窗口API测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试标签页API
        window.testTabAPI = async function() {
            try {
                const { getAllTabs, createTab } = await import('/src/api/tab-api.js');
                
                // 测试获取所有标签页
                const tabs = await getAllTabs();
                
                // 测试创建标签页
                const newTab = await createTab({
                    windowId: 'test-window',
                    url: 'https://example.com',
                    title: '测试标签页'
                });
                
                setTestResult(4, true, `✅ 标签页API功能正常\n现有标签页数: ${tabs.length}\n新建标签页ID: ${newTab.id}\n新建标签页标题: ${newTab.title}`);
            } catch (error) {
                setTestResult(4, false, `❌ 标签页API测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试消息处理器
        window.testMessageHandler = async function() {
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    const response = await chrome.runtime.sendMessage({
                        type: 'GET_BROWSER_DATA',
                        payload: { includeIncognito: false }
                    });
                    
                    // 处理MessageHandler的响应格式
                    const data = response && response.data ? response.data : response;
                    
                    if (data && data.windows) {
                        setTestResult(5, true, `✅ 消息处理器工作正常\n响应格式: ${response.status ? 'MessageHandler格式' : '直接格式'}\n获取到窗口数: ${data.windows.length}\n时间戳: ${data.timestamp}`);
                    } else {
                        setTestResult(5, false, `❌ 消息处理器响应异常\n响应内容: ${JSON.stringify(response)}`);
                    }
                } else {
                    setTestResult(5, false, '❌ Chrome扩展环境不可用');
                }
            } catch (error) {
                setTestResult(5, false, `❌ 消息处理器测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试浏览器数据导入
        window.testBrowserImport = async function() {
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await chrome.runtime.sendMessage({
                        type: 'GET_BROWSER_DATA',
                        payload: { includeIncognito: false }
                    });
                    
                    const data = response && response.data ? response.data : response;
                    
                    if (data && data.windows && data.windows.length > 0) {
                        const totalTabs = data.windows.reduce((sum, win) => sum + (win.tabs ? win.tabs.length : 0), 0);
                        const windowInfo = data.windows.map(w => `窗口${w.id}(${w.tabs ? w.tabs.length : 0}个标签)`).join(', ');
                        
                        setTestResult(6, true, `✅ 浏览器数据导入功能正常\n窗口数: ${data.windows.length}\n总标签页数: ${totalTabs}\n窗口详情: ${windowInfo}`);
                    } else {
                        setTestResult(6, false, `❌ 未获取到浏览器数据\n响应: ${JSON.stringify(data)}`);
                    }
                } else {
                    setTestResult(6, false, '❌ Chrome扩展环境不可用');
                }
            } catch (error) {
                setTestResult(6, false, `❌ 浏览器数据导入测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试创建测试数据
        window.testCreateData = async function() {
            try {
                const { createWindow } = await import('/src/api/window-api.js');
                const { createTab } = await import('/src/api/tab-api.js');
                
                // 创建测试窗口
                const testWindow = await createWindow({
                    name: '测试窗口集成',
                    color: '#00ff00'
                });
                
                // 为测试窗口创建标签页
                const testTabs = [];
                for (let i = 1; i <= 3; i++) {
                    const tab = await createTab({
                        windowId: testWindow.windowId,
                        url: `https://test${i}.example.com`,
                        title: `测试标签页 ${i}`
                    });
                    testTabs.push(tab);
                }
                
                setTestResult(7, true, `✅ 测试数据创建成功\n窗口ID: ${testWindow.windowId}\n窗口名: ${testWindow.name}\n创建标签页数: ${testTabs.length}\n标签页: ${testTabs.map(t => t.title).join(', ')}`);
            } catch (error) {
                setTestResult(7, false, `❌ 创建测试数据失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试数据导入导出
        window.testImportExport = async function() {
            try {
                const { getAllWindows } = await import('/src/api/window-api.js');
                const { getAllTabs } = await import('/src/api/tab-api.js');
                
                // 获取当前数据
                const windows = await getAllWindows();
                const tabs = await getAllTabs();
                
                // 模拟导出数据格式
                const exportData = {
                    version: '1.0.0',
                    timestamp: new Date().toISOString(),
                    windows: windows.map(w => ({
                        id: w.windowId,
                        name: w.name,
                        color: w.color,
                        tabs: tabs.filter(t => t.windowId === w.windowId)
                    }))
                };
                
                setTestResult(8, true, `✅ 数据导入导出功能正常\n导出窗口数: ${exportData.windows.length}\n导出标签页数: ${tabs.length}\n数据大小: ${JSON.stringify(exportData).length} 字符`);
            } catch (error) {
                setTestResult(8, false, `❌ 数据导入导出测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 测试主页面加载
        window.testMainPageLoad = async function() {
            try {
                // 检查主页面文件是否存在
                const htmlResponse = await fetch('/src/pages/management.html');
                const cssResponse = await fetch('/src/pages/management.css');
                const jsResponse = await fetch('/src/pages/management.js');
                
                if (htmlResponse.ok && cssResponse.ok && jsResponse.ok) {
                    const htmlContent = await htmlResponse.text();
                    const hasModuleScript = htmlContent.includes('type="module"');
                    const hasImportBrowserBtn = htmlContent.includes('btn-import-browser');
                    
                    setTestResult(9, true, `✅ 主页面文件完整\nHTML状态: ${htmlResponse.status}\nCSS状态: ${cssResponse.status}\nJS状态: ${jsResponse.status}\n模块脚本: ${hasModuleScript ? '已配置' : '未配置'}\n导入按钮: ${hasImportBrowserBtn ? '已添加' : '未添加'}`);
                } else {
                    setTestResult(9, false, `❌ 主页面文件缺失\nHTML: ${htmlResponse.status}\nCSS: ${cssResponse.status}\nJS: ${jsResponse.status}`);
                }
            } catch (error) {
                setTestResult(9, false, `❌ 主页面加载测试失败: ${error.message}\n${error.stack}`);
            }
        };

        // 运行所有测试
        window.runAllTests = async function() {
            const tests = [
                window.testUUIDImport,
                window.testStorageAPI,
                window.testWindowAPI,
                window.testTabAPI,
                window.testMessageHandler,
                window.testBrowserImport,
                window.testCreateData,
                window.testImportExport,
                window.testMainPageLoad
            ];

            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        };

        // 打开主页面
        window.openMainPage = function() {
            window.open('/src/pages/management.html', '_blank');
        };

        // 生成报告
        window.generateReport = function() {
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            const total = Object.keys(testResults).length;
            
            const report = `
MyTab3 插件修复验证报告
====================
测试时间: ${new Date().toLocaleString()}
总测试项: ${total}
通过: ${passed}
失败: ${failed}
通过率: ${total > 0 ? Math.round(passed / total * 100) : 0}%

修复状态:
${total === totalTests && failed === 0 ? '🎉 所有修复验证通过！插件功能已完全恢复。' : '⚠️ 仍有部分测试未通过，需要进一步检查。'}

建议:
${failed === 0 ? '✅ 可以正常使用插件的所有功能' : '❌ 请检查失败的测试项并进行相应修复'}
            `;
            
            alert(report);
        };
    </script>
</body>
</html>
