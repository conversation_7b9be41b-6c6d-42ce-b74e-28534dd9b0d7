# Chrome扩展基础框架需求文档

## 1. 项目整体架构

### 1.1 Chrome扩展文件结构

```
project-root/
├── manifest.json                 # 扩展配置文件
├── src/
│   ├── background/              # 后台脚本
│   │   ├── index.js            # 后台脚本入口
│   │   ├── services/           # 后台服务
│   │   │   ├── tab-manager.js # 标签页管理服务
│   │   │   ├── window-manager.js # 窗口管理服务
│   │   │   ├── storage-service.js # 存储服务
│   │   │   └── message-handler.js # 消息处理服务
│   │   └── utils/              # 工具函数
│   ├── popup/                  # 弹出窗口
│   │   ├── popup.html         # 弹出窗口HTML
│   │   ├── popup.js           # 弹出窗口脚本
│   │   └── popup.css          # 弹出窗口样式
│   ├── options/                # 选项页面
│   │   ├── options.html       # 选项页面HTML
│   │   ├── options.js         # 选项页面脚本
│   │   └── options.css        # 选项页面样式
│   ├── content/                # 内容脚本
│   │   └── content.js         # 内容脚本
│   ├── shared/                 # 共享代码
│   │   ├── constants.js       # 常量定义
│   │   ├── models/            # 数据模型
│   │   │   ├── tab.js        # Tab模型
│   │   │   ├── window.js     # Window模型
│   │   │   └── settings.js   # Settings模型
│   │   └── utils/             # 共享工具函数
│   └── assets/                 # 资源文件
│       ├── icons/             # 图标
│       └── images/            # 图片
├── _locales/                   # 国际化文件
│   ├── en/
│   │   └── messages.json
│   └── zh_CN/
│       └── messages.json
└── docs/                       # 文档
```

### 1.2 manifest.json 配置详细说明

```json
{
  "manifest_version": 3,
  "name": "__MSG_extensionName__",
  "version": "1.0.0",
  "description": "__MSG_extensionDescription__",
  "default_locale": "en",
  
  // 权限配置
  "permissions": [
    "tabs",              // 访问标签页信息
    "storage",           // 本地存储
    "activeTab",         // 访问当前活动标签
    "contextMenus",      // 右键菜单
    "bookmarks",         // 书签管理
    "history",           // 历史记录
    "windows"            // 窗口管理
  ],
  
  // 主机权限
  "host_permissions": [
    "<all_urls>"         // 访问所有网站（根据需要限制）
  ],
  
  // 后台服务工作者
  "background": {
    "service_worker": "src/background/index.js",
    "type": "module"
  },
  
  // 弹出窗口
  "action": {
    "default_popup": "src/popup/popup.html",
    "default_icon": {
      "16": "src/assets/icons/icon16.png",
      "48": "src/assets/icons/icon48.png",
      "128": "src/assets/icons/icon128.png"
    },
    "default_title": "__MSG_extensionName__"
  },
  
  // 选项页面
  "options_page": "src/options/options.html",
  
  // 内容脚本
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["src/content/content.js"],
      "run_at": "document_idle"
    }
  ],
  
  // 图标
  "icons": {
    "16": "src/assets/icons/icon16.png",
    "48": "src/assets/icons/icon48.png",
    "128": "src/assets/icons/icon128.png"
  },
  
  // Web可访问资源
  "web_accessible_resources": [
    {
      "resources": ["src/assets/*"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

### 1.3 模块依赖关系图

```
┌─────────────────────────────────────────────────────────────┐
│                        Background Service                     │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                   Message Handler                     │   │
│  │  (中央消息路由，处理所有模块间的通信)                 │   │
│  └──────────────────────┬───────────────────────────────┘   │
│           ┌─────────────┴─────────────┐                     │
│           │                           │                     │
│  ┌────────▼────────┐        ┌────────▼────────┐           │
│  │  Tab Manager    │        │ Window Manager  │           │
│  │  (标签页管理)    │        │  (窗口管理)      │           │
│  └────────┬────────┘        └────────┬────────┘           │
│           │                           │                     │
│           └─────────┬─────────────────┘                     │
│                     │                                       │
│            ┌────────▼────────┐                             │
│            │ Storage Service │                             │
│            │   (数据持久化)   │                             │
│            └─────────────────┘                             │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌────────▼────────┐   ┌───────▼────────┐
│    Popup      │    │    Options      │   │   Content      │
│   (弹出窗口)   │    │   (选项页面)     │   │  (内容脚本)    │
└───────────────┘    └─────────────────┘   └────────────────┘
```

## 2. 核心数据模型定义

### 2.1 Tab数据模型

```javascript
// src/shared/models/tab.js
export class TabModel {
  constructor(data = {}) {
    this.id = data.id || null;                    // Chrome标签页ID
    this.windowId = data.windowId || null;        // 所属窗口ID
    this.index = data.index || 0;                 // 标签页索引
    this.url = data.url || '';                    // URL地址
    this.title = data.title || '';                // 标题
    this.favIconUrl = data.favIconUrl || '';      // 图标URL
    this.status = data.status || 'loading';       // 状态: loading/complete
    this.active = data.active || false;           // 是否激活
    this.pinned = data.pinned || false;           // 是否固定
    this.audible = data.audible || false;         // 是否有声音
    this.mutedInfo = data.mutedInfo || {          // 静音信息
      muted: false,
      reason: undefined,
      extensionId: undefined
    };
    this.incognito = data.incognito || false;     // 是否隐身模式
    this.groupId = data.groupId || -1;            // 标签组ID
    this.lastAccessed = data.lastAccessed || Date.now(); // 最后访问时间
    this.customData = data.customData || {};      // 自定义数据
  }

  // 序列化为存储格式
  toStorage() {
    return {
      id: this.id,
      windowId: this.windowId,
      index: this.index,
      url: this.url,
      title: this.title,
      favIconUrl: this.favIconUrl,
      status: this.status,
      active: this.active,
      pinned: this.pinned,
      audible: this.audible,
      mutedInfo: this.mutedInfo,
      incognito: this.incognito,
      groupId: this.groupId,
      lastAccessed: this.lastAccessed,
      customData: this.customData
    };
  }

  // 从Chrome Tab API对象创建
  static fromChromeTab(chromeTab) {
    return new TabModel({
      id: chromeTab.id,
      windowId: chromeTab.windowId,
      index: chromeTab.index,
      url: chromeTab.url,
      title: chromeTab.title,
      favIconUrl: chromeTab.favIconUrl,
      status: chromeTab.status,
      active: chromeTab.active,
      pinned: chromeTab.pinned,
      audible: chromeTab.audible,
      mutedInfo: chromeTab.mutedInfo,
      incognito: chromeTab.incognito,
      groupId: chromeTab.groupId,
      lastAccessed: Date.now()
    });
  }

  // 更新标签页数据
  update(updates) {
    Object.keys(updates).forEach(key => {
      if (this.hasOwnProperty(key)) {
        this[key] = updates[key];
      }
    });
    this.lastAccessed = Date.now();
    return this;
  }

  // 验证数据有效性
  validate() {
    const errors = [];
    if (!this.id || typeof this.id !== 'number') {
      errors.push('Invalid tab ID');
    }
    if (!this.windowId || typeof this.windowId !== 'number') {
      errors.push('Invalid window ID');
    }
    if (this.url && !this.isValidUrl(this.url)) {
      errors.push('Invalid URL');
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // URL验证
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return url.startsWith('chrome://') || 
             url.startsWith('chrome-extension://') ||
             url === 'about:blank';
    }
  }
}
```

### 2.2 Window数据模型

```javascript
// src/shared/models/window.js
export class WindowModel {
  constructor(data = {}) {
    this.id = data.id || null;                    // Chrome窗口ID
    this.focused = data.focused || false;         // 是否聚焦
    this.state = data.state || 'normal';          // 状态: normal/minimized/maximized/fullscreen
    this.type = data.type || 'normal';            // 类型: normal/popup/panel/app/devtools
    this.incognito = data.incognito || false;     // 是否隐身模式
    this.alwaysOnTop = data.alwaysOnTop || false; // 是否置顶
    this.left = data.left || 0;                   // 左边距
    this.top = data.top || 0;                     // 上边距
    this.width = data.width || 800;               // 宽度
    this.height = data.height || 600;             // 高度
    this.tabs = data.tabs || [];                  // 标签页ID列表
    this.customData = data.customData || {};      // 自定义数据
    this.createdAt = data.createdAt || Date.now(); // 创建时间
    this.lastModified = data.lastModified || Date.now(); // 最后修改时间
  }

  // 序列化为存储格式
  toStorage() {
    return {
      id: this.id,
      focused: this.focused,
      state: this.state,
      type: this.type,
      incognito: this.incognito,
      alwaysOnTop: this.alwaysOnTop,
      left: this.left,
      top: this.top,
      width: this.width,
      height: this.height,
      tabs: this.tabs,
      customData: this.customData,
      createdAt: this.createdAt,
      lastModified: this.lastModified
    };
  }

  // 从Chrome Window API对象创建
  static fromChromeWindow(chromeWindow) {
    return new WindowModel({
      id: chromeWindow.id,
      focused: chromeWindow.focused,
      state: chromeWindow.state,
      type: chromeWindow.type,
      incognito: chromeWindow.incognito,
      alwaysOnTop: chromeWindow.alwaysOnTop,
      left: chromeWindow.left,
      top: chromeWindow.top,
      width: chromeWindow.width,
      height: chromeWindow.height,
      tabs: chromeWindow.tabs ? chromeWindow.tabs.map(tab => tab.id) : [],
      createdAt: Date.now()
    });
  }

  // 更新窗口数据
  update(updates) {
    Object.keys(updates).forEach(key => {
      if (this.hasOwnProperty(key)) {
        this[key] = updates[key];
      }
    });
    this.lastModified = Date.now();
    return this;
  }

  // 添加标签页
  addTab(tabId, index = -1) {
    if (!this.tabs.includes(tabId)) {
      if (index >= 0 && index < this.tabs.length) {
        this.tabs.splice(index, 0, tabId);
      } else {
        this.tabs.push(tabId);
      }
      this.lastModified = Date.now();
    }
    return this;
  }

  // 移除标签页
  removeTab(tabId) {
    const index = this.tabs.indexOf(tabId);
    if (index !== -1) {
      this.tabs.splice(index, 1);
      this.lastModified = Date.now();
    }
    return this;
  }

  // 移动标签页
  moveTab(tabId, newIndex) {
    const currentIndex = this.tabs.indexOf(tabId);
    if (currentIndex !== -1 && newIndex >= 0 && newIndex < this.tabs.length) {
      this.tabs.splice(currentIndex, 1);
      this.tabs.splice(newIndex, 0, tabId);
      this.lastModified = Date.now();
    }
    return this;
  }

  // 验证数据有效性
  validate() {
    const errors = [];
    if (!this.id || typeof this.id !== 'number') {
      errors.push('Invalid window ID');
    }
    if (!['normal', 'minimized', 'maximized', 'fullscreen'].includes(this.state)) {
      errors.push('Invalid window state');
    }
    if (!['normal', 'popup', 'panel', 'app', 'devtools'].includes(this.type)) {
      errors.push('Invalid window type');
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

### 2.3 Settings数据模型

```javascript
// src/shared/models/settings.js
export class SettingsModel {
  constructor(data = {}) {
    // 通用设置
    this.general = {
      theme: data.general?.theme || 'light',           // 主题: light/dark/auto
      language: data.general?.language || 'en',        // 语言
      startupBehavior: data.general?.startupBehavior || 'restore', // 启动行为: restore/blank
      autoSave: data.general?.autoSave || true,        // 自动保存
      autoSaveInterval: data.general?.autoSaveInterval || 300000, // 自动保存间隔(ms)
      ...data.general
    };

    // 标签页管理设置
    this.tabManagement = {
      groupSimilarTabs: data.tabManagement?.groupSimilarTabs || false, // 自动分组相似标签
      duplicateDetection: data.tabManagement?.duplicateDetection || true, // 重复检测
      suspendInactiveTabs: data.tabManagement?.suspendInactiveTabs || false, // 挂起非活动标签
      suspendTimeout: data.tabManagement?.suspendTimeout || 1800000, // 挂起超时(ms)
      maxTabsPerWindow: data.tabManagement?.maxTabsPerWindow || 100, // 每窗口最大标签数
      ...data.tabManagement
    };

    // 显示设置
    this.display = {
      showFavicons: data.display?.showFavicons || true,         // 显示图标
      showPreview: data.display?.showPreview || true,          // 显示预览
      compactMode: data.display?.compactMode || false,         // 紧凑模式
      showTabCount: data.display?.showTabCount || true,        // 显示标签计数
      showMemoryUsage: data.display?.showMemoryUsage || false, // 显示内存使用
      ...data.display
    };

    // 搜索设置
    this.search = {
      enableFuzzySearch: data.search?.enableFuzzySearch || true,    // 模糊搜索
      searchInUrl: data.search?.searchInUrl || true,               // 在URL中搜索
      searchInTitle: data.search?.searchInTitle || true,           // 在标题中搜索
      searchHistory: data.search?.searchHistory || false,          // 搜索历史
      maxSearchResults: data.search?.maxSearchResults || 50,       // 最大搜索结果
      ...data.search
    };

    // 快捷键设置
    this.shortcuts = {
      openPopup: data.shortcuts?.openPopup || 'Alt+T',           // 打开弹窗
      searchTabs: data.shortcuts?.searchTabs || 'Ctrl+Shift+F',  // 搜索标签
      closeTab: data.shortcuts?.closeTab || 'Ctrl+W',            // 关闭标签
      restoreTab: data.shortcuts?.restoreTab || 'Ctrl+Shift+T',  // 恢复标签
      ...data.shortcuts
    };

    // 同步设置
    this.sync = {
      enableSync: data.sync?.enableSync || false,              // 启用同步
      syncSettings: data.sync?.syncSettings || true,           // 同步设置
      syncTabs: data.sync?.syncTabs || false,                  // 同步标签
      syncBookmarks: data.sync?.syncBookmarks || false,        // 同步书签
      ...data.sync
    };

    // 隐私设置
    this.privacy = {
      collectAnalytics: data.privacy?.collectAnalytics || false,  // 收集分析数据
      clearDataOnExit: data.privacy?.clearDataOnExit || false,   // 退出时清除数据
      excludeIncognito: data.privacy?.excludeIncognito || true,  // 排除隐身模式
      ...data.privacy
    };

    // 元数据
    this.metadata = {
      version: data.metadata?.version || '1.0.0',
      lastModified: data.metadata?.lastModified || Date.now(),
      installDate: data.metadata?.installDate || Date.now(),
      ...data.metadata
    };
  }

  // 序列化为存储格式
  toStorage() {
    return {
      general: this.general,
      tabManagement: this.tabManagement,
      display: this.display,
      search: this.search,
      shortcuts: this.shortcuts,
      sync: this.sync,
      privacy: this.privacy,
      metadata: {
        ...this.metadata,
        lastModified: Date.now()
      }
    };
  }

  // 更新设置
  update(category, updates) {
    if (this.hasOwnProperty(category) && typeof this[category] === 'object') {
      this[category] = {
        ...this[category],
        ...updates
      };
      this.metadata.lastModified = Date.now();
    }
    return this;
  }

  // 重置到默认设置
  reset(category = null) {
    if (category && this.hasOwnProperty(category)) {
      this[category] = new SettingsModel()[category];
    } else {
      const defaults = new SettingsModel();
      Object.keys(defaults).forEach(key => {
        if (key !== 'metadata') {
          this[key] = defaults[key];
        }
      });
    }
    this.metadata.lastModified = Date.now();
    return this;
  }

  // 验证设置有效性
  validate() {
    const errors = [];
    
    // 验证主题
    if (!['light', 'dark', 'auto'].includes(this.general.theme)) {
      errors.push('Invalid theme setting');
    }
    
    // 验证自动保存间隔
    if (this.general.autoSaveInterval < 60000) {
      errors.push('Auto-save interval must be at least 60 seconds');
    }
    
    // 验证最大标签数
    if (this.tabManagement.maxTabsPerWindow < 1 || this.tabManagement.maxTabsPerWindow > 500) {
      errors.push('Max tabs per window must be between 1 and 500');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 导出设置
  export() {
    return JSON.stringify(this.toStorage(), null, 2);
  }

  // 导入设置
  static import(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return new SettingsModel(data);
    } catch (error) {
      throw new Error('Invalid settings format');
    }
  }
}
```

### 2.4 数据模型关系

```javascript
// 数据模型关系定义
export const ModelRelationships = {
  // Window包含多个Tab
  WindowToTabs: {
    type: 'one-to-many',
    parent: 'Window',
    child: 'Tab',
    foreignKey: 'windowId',
    cascade: true  // 删除Window时删除所有关联的Tab
  },

  // Tab属于一个Window
  TabToWindow: {
    type: 'many-to-one',
    parent: 'Tab',
    child: 'Window',
    foreignKey: 'windowId',
    required: true
  },

  // Tab可以属于一个TabGroup
  TabToGroup: {
    type: 'many-to-one',
    parent: 'Tab',
    child: 'TabGroup',
    foreignKey: 'groupId',
    required: false
  },

  // Settings是全局唯一的
  GlobalSettings: {
    type: 'singleton',
    model: 'Settings',
    storageKey: 'global_settings'
  }
};

// 数据验证规则
export const ValidationRules = {
  Tab: {
    id: { type: 'number', required: true, min: 1 },
    windowId: { type: 'number', required: true, min: 1 },
    url: { type: 'string', required: true, validator: 'isValidUrl' },
    title: { type: 'string', required: false, maxLength: 500 },
    index: { type: 'number', required: true, min: 0 }
  },
  Window: {
    id: { type: 'number', required: true, min: 1 },
    state: { type: 'enum', values: ['normal', 'minimized', 'maximized', 'fullscreen'] },
    type: { type: 'enum', values: ['normal', 'popup', 'panel', 'app', 'devtools'] },
    width: { type: 'number', required: true, min: 100, max: 9999 },
    height: { type: 'number', required: true, min: 100, max: 9999 }
  },
  Settings: {
    'general.theme': { type: 'enum', values: ['light', 'dark', 'auto'] },
    'general.autoSaveInterval': { type: 'number', min: 60000, max: 3600000 },
    'tabManagement.maxTabsPerWindow': { type: 'number', min: 1, max: 500 }
  }
};
```

## 3. 模块间通信机制

### 3.1 Chrome消息传递API规范

```javascript
// src/shared/constants.js
// 消息类型定义
export const MessageTypes = {
  // Tab相关
  TAB_CREATE: 'TAB_CREATE',
  TAB_UPDATE: 'TAB_UPDATE',
  TAB_REMOVE: 'TAB_REMOVE',
  TAB_MOVE: 'TAB_MOVE',
  TAB_ACTIVATE: 'TAB_ACTIVATE',
  TAB_QUERY: 'TAB_QUERY',
  TAB_GET: 'TAB_GET',
  TAB_GROUP: 'TAB_GROUP',
  TAB_UNGROUP: 'TAB_UNGROUP',
  TAB_SUSPEND: 'TAB_SUSPEND',
  TAB_RESTORE: 'TAB_RESTORE',
  
  // Window相关
  WINDOW_CREATE: 'WINDOW_CREATE',
  WINDOW_UPDATE: 'WINDOW_UPDATE',
  WINDOW_REMOVE: 'WINDOW_REMOVE',
  WINDOW_GET: 'WINDOW_GET',
  WINDOW_GET_ALL: 'WINDOW_GET_ALL',
  WINDOW_FOCUS: 'WINDOW_FOCUS',
  
  // Storage相关
  STORAGE_GET: 'STORAGE_GET',
  STORAGE_SET: 'STORAGE_SET',
  STORAGE_REMOVE: 'STORAGE_REMOVE',
  STORAGE_CLEAR: 'STORAGE_CLEAR',
  
  // Settings相关
  SETTINGS_GET: 'SETTINGS_GET',
  SETTINGS_UPDATE: 'SETTINGS_UPDATE',
  SETTINGS_RESET: 'SETTINGS_RESET',
  SETTINGS_EXPORT: 'SETTINGS_EXPORT',
  SETTINGS_IMPORT: 'SETTINGS_IMPORT',
  
  // 状态同步
  STATE_SYNC: 'STATE_SYNC',
  STATE_REQUEST: 'STATE_REQUEST',
  STATE_UPDATE: 'STATE_UPDATE',
  
  // 错误处理
  ERROR: 'ERROR',
  ERROR_CLEAR: 'ERROR_CLEAR'
};

// 消息响应状态
export const ResponseStatus = {
  SUCCESS: 'success',
  ERROR: 'error',
  PENDING: 'pending'
};
```

### 3.2 消息处理器基类

```javascript
// src/background/services/message-handler.js
export class MessageHandler {
  constructor() {
    this.handlers = new Map();
    this.middleware = [];
    this.initializeHandlers();
  }

  // 初始化消息监听
  initialize() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      // 异步处理消息
      this.handleMessage(request, sender)
        .then(response => {
          sendResponse({
            status: ResponseStatus.SUCCESS,
            data: response,
            timestamp: Date.now()
          });
        })
        .catch(error => {
          console.error('Message handling error:', error);
          sendResponse({
            status: ResponseStatus.ERROR,
            error: {
              message: error.message,
              code: error.code || 'UNKNOWN_ERROR',
              details: error.details
            },
            timestamp: Date.now()
          });
        });
      
      // 返回true表示异步响应
      return true;
    });
  }

  // 注册消息处理器
  register(type, handler) {
    if (!type || typeof handler !== 'function') {
      throw new Error('Invalid handler registration');
    }
    this.handlers.set(type, handler);
  }

  // 注册中间件
  use(middleware) {
    if (typeof middleware !== 'function') {
      throw new Error('Middleware must be a function');
    }
    this.middleware.push(middleware);
  }

  // 处理消息
  async handleMessage(request, sender) {
    const { type, payload } = request;
    
    // 验证消息格式
    if (!type) {
      throw new Error('Message type is required');
    }
    
    // 执行中间件
    for (const middleware of this.middleware) {
      const result = await middleware(request, sender);
      if (result === false) {
        throw new Error('Message rejected by middleware');
      }
    }
    
    // 获取处理器
    const handler = this.handlers.get(type);
    if (!handler) {
      throw new Error(`No handler registered for message type: ${type}`);
    }
    
    // 执行处理器
    return await handler(payload, sender);
  }

  // 发送消息到其他模块
  async sendMessage(type, payload, tabId = null) {
    const message = {
      type,
      payload,
      timestamp: Date.now()
    };

    try {
      if (tabId) {
        // 发送到特定标签页
        return await chrome.tabs.sendMessage(tabId, message);
      } else {
        // 发送到扩展内部
        return await chrome.runtime.sendMessage(message);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  // 广播消息到所有标签页
  async broadcast(type, payload) {
    const message = {
      type,
      payload,
      timestamp: Date.now()
    };

    const tabs = await chrome.tabs.query({});
    const promises = tabs.map(tab => 
      this.sendMessage(type, payload, tab.id).catch(err => {
        console.warn(`Failed to send to tab ${tab.id}:`, err);
        return null;
      })
    );

    return await Promise.all(promises);
  }

  // 初始化默认处理器
  initializeHandlers() {
    // 注册Tab相关处理器
    this.register(MessageTypes.TAB_CREATE, async (payload) => {
      // 实现标签创建逻辑
      return await TabManager.create(payload);
    });

    this.register(MessageTypes.TAB_UPDATE, async (payload) => {
      // 实现标签更新逻辑
      return await TabManager.update(payload.tabId, payload.updates);
    });

    this.register(MessageTypes.TAB_REMOVE, async (payload) => {
      // 实现标签删除逻辑
      return await TabManager.remove(payload.tabId);
    });

    // 注册Window相关处理器
    this.register(MessageTypes.WINDOW_CREATE, async (payload) => {
      return await WindowManager.create(payload);
    });

    this.register(MessageTypes.WINDOW_UPDATE, async (payload) => {
      return await WindowManager.update(payload.windowId, payload.updates);
    });

    // 注册Storage相关处理器
    this.register(MessageTypes.STORAGE_GET, async (payload) => {
      return await StorageService.get(payload.keys);
    });

    this.register(MessageTypes.STORAGE_SET, async (payload) => {
      return await StorageService.set(payload.data);
    });

    // 注册Settings相关处理器
    this.register(MessageTypes.SETTINGS_GET, async (payload) => {
      return await SettingsService.get(payload.category);
    });

    this.register(MessageTypes.SETTINGS_UPDATE, async (payload) => {
      return await SettingsService.update(payload.category, payload.updates);
    });
  }
}

// 创建全局消息处理器实例
export const messageHandler = new MessageHandler();
```

### 3.3 事件总线设计

```javascript
// src/shared/utils/event-bus.js
export class EventBus {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
    this.maxListeners = 10;
  }

  // 订阅事件
  on(event, callback, context = null) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    
    const listeners = this.events.get(event);
    if (listeners.length >= this.maxListeners) {
      console.warn(`Max listeners (${this.maxListeners}) for event "${event}" exceeded`);
    }
    
    listeners.push({ callback, context });
    return this;
  }

  // 订阅一次性事件
  once(event, callback, context = null) {
    if (!this.onceEvents.has(event)) {
      this.onceEvents.set(event, []);
    }
    
    this.onceEvents.get(event).push({ callback, context });
    return this;
  }

  // 取消订阅
  off(event, callback = null) {
    if (!callback) {
      // 移除所有该事件的监听器
      this.events.delete(event);
      this.onceEvents.delete(event);
      return this;
    }

    // 移除特定的监听器
    const removeFromList = (list) => {
      if (!list) return;
      const index = list.findIndex(listener => listener.callback === callback);
      if (index !== -1) {
        list.splice(index, 1);
      }
    };

    removeFromList(this.events.get(event));
    removeFromList(this.onceEvents.get(event));
    return this;
  }

  // 触发事件
  emit(event, ...args) {
    const listeners = this.events.get(event) || [];
    const onceListeners = this.onceEvents.get(event) || [];
    
    // 清空一次性监听器
    this.onceEvents.delete(event);
    
    // 执行所有监听器
    [...listeners, ...onceListeners].forEach(({ callback, context }) => {
      try {
        callback.apply(context, args);
      } catch (error) {
        console.error(`Error in event listener for "${event}":`, error);
      }
    });
    
    return this;
  }

  // 异步触发事件
  async emitAsync(event, ...args) {
    const listeners = this.events.get(event) || [];
    const onceListeners = this.onceEvents.get(event) || [];
    
    // 清空一次性监听器
    this.onceEvents.delete(event);
    
    // 并行执行所有监听器
    const promises = [...listeners, ...onceListeners].map(({ callback, context }) => {
      return Promise.resolve()
        .then(() => callback.apply(context, args))
        .catch(error => {
          console.error(`Error in async event listener for "${event}":`, error);
          throw error;
        });
    });
    
    return await Promise.all(promises);
  }

  // 清空所有事件监听器
  clear() {
    this.events.clear();
    this.onceEvents.clear();
    return this;
  }

  // 获取事件监听器数量
  listenerCount(event) {
    const regular = this.events.get(event)?.length || 0;
    const once = this.onceEvents.get(event)?.length || 0;
    return regular + once;
  }

  // 设置最大监听器数量
  setMaxListeners(max) {
    this.maxListeners = max;
    return this;
  }
}

// 创建全局事件总线实例
export const globalEventBus = new EventBus();

// 预定义事件类型
export const Events = {
  // Tab事件
  TAB_CREATED: 'tab:created',
  TAB_UPDATED: 'tab:updated',
  TAB_REMOVED: 'tab:removed',
  TAB_ACTIVATED: 'tab:activated',
  TAB_MOVED: 'tab:moved',
  
  // Window事件
  WINDOW_CREATED: 'window:created',
  WINDOW_REMOVED: 'window:removed',
  WINDOW_FOCUSED: 'window:focused',
  
  // Storage事件
  STORAGE_CHANGED: 'storage:changed',
  
  // Settings事件
  SETTINGS_CHANGED: 'settings:changed',
  
  // 系统事件
  EXTENSION_INSTALLED: 'extension:installed',
  EXTENSION_UPDATED: 'extension:updated',
  
  // 错误事件
  ERROR_OCCURRED: 'error:occurred',
  ERROR_RESOLVED: 'error:resolved'
};
```

### 3.4 模块间API接口定义

```javascript
// src/shared/api/tab-api.js
export class TabAPI {
  static async create(options) {
    return await messageHandler.sendMessage(MessageTypes.TAB_CREATE, options);
  }

  static async get(tabId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_GET, { tabId });
  }

  static async update(tabId, updates) {
    return await messageHandler.sendMessage(MessageTypes.TAB_UPDATE, { tabId, updates });
  }

  static async remove(tabId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_REMOVE, { tabId });
  }

  static async move(tabId, moveInfo) {
    return await messageHandler.sendMessage(MessageTypes.TAB_MOVE, { tabId, moveInfo });
  }

  static async query(queryInfo) {
    return await messageHandler.sendMessage(MessageTypes.TAB_QUERY, queryInfo);
  }

  static async activate(tabId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_ACTIVATE, { tabId });
  }

  static async group(tabIds, groupId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_GROUP, { tabIds, groupId });
  }

  static async ungroup(tabIds) {
    return await messageHandler.sendMessage(MessageTypes.TAB_UNGROUP, { tabIds });
  }

  static async suspend(tabId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_SUSPEND, { tabId });
  }

  static async restore(tabId) {
    return await messageHandler.sendMessage(MessageTypes.TAB_RESTORE, { tabId });
  }
}

// src/shared/api/window-api.js
export class WindowAPI {
  static async create(options) {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_CREATE, options);
  }

  static async get(windowId) {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_GET, { windowId });
  }

  static async getAll() {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_GET_ALL);
  }

  static async update(windowId, updates) {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_UPDATE, { windowId, updates });
  }

  static async remove(windowId) {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_REMOVE, { windowId });
  }

  static async focus(windowId) {
    return await messageHandler.sendMessage(MessageTypes.WINDOW_FOCUS, { windowId });
  }
}

// src/shared/api/storage-api.js
export class StorageAPI {
  static async get(keys) {
    return await messageHandler.sendMessage(MessageTypes.STORAGE_GET, { keys });
  }

  static async set(data) {
    return await messageHandler.sendMessage(MessageTypes.STORAGE_SET, { data });
  }

  static async remove(keys) {
    return await messageHandler.sendMessage(MessageTypes.STORAGE_REMOVE, { keys });
  }

  static async clear() {
    return await messageHandler.sendMessage(MessageTypes.STORAGE_CLEAR);
  }
}

// src/shared/api/settings-api.js
export class SettingsAPI {
  static async get(category = null) {
    return await messageHandler.sendMessage(MessageTypes.SETTINGS_GET, { category });
  }

  static async update(category, updates) {
    return await messageHandler.sendMessage(MessageTypes.SETTINGS_UPDATE, { category, updates });
  }

  static async reset(category = null) {
    return await messageHandler.sendMessage(MessageTypes.SETTINGS_RESET, { category });
  }

  static async export() {
    return await messageHandler.sendMessage(MessageTypes.SETTINGS_EXPORT);
  }

  static async import(settingsData) {
    return await messageHandler.sendMessage(MessageTypes.SETTINGS_IMPORT, { settingsData });
  }
}
```

## 4. 全局状态管理

### 4.1 状态管理方案

```javascript
// src/background/services/state-manager.js
export class StateManager {
  constructor() {
    this.state = {
      tabs: new Map(),        // tabId -> TabModel
      windows: new Map(),     // windowId -> WindowModel
      settings: null,         // SettingsModel
      ui: {
        activeTab: null,
        activeWindow: null,
        searchQuery: '',
        filters: {},
        sortBy: 'lastAccessed'
      },
      session: {
        startTime: Date.now(),
        lastActivity: Date.now(),
        actionCount: 0
      }
    };
    
    this.subscribers = new Map();
    this.history = [];
    this.maxHistorySize = 50;
    this.batchUpdates = [];
    this.batchTimeout = null;
  }

  // 获取当前状态
  getState(path = null) {
    if (!path) {
      return this.deepClone(this.state);
    }
    
    // 支持点语法访问嵌套属性
    const keys = path.split('.');
    let value = this.state;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return undefined;
      }
    }
    
    return this.deepClone(value);
  }

  // 更新状态
  setState(updates, options = {}) {
    const { batch = false, silent = false } = options;
    
    if (batch) {
      this.batchUpdate(updates);
      return;
    }
    
    // 保存历史记录
    if (!silent) {
      this.saveHistory();
    }
    
    // 深度合并更新
    this.state = this.deepMerge(this.state, updates);
    
    // 通知订阅者
    if (!silent) {
      this.notifySubscribers(updates);
    }
    
    // 更新最后活动时间
    this.state.session.lastActivity = Date.now();
    this.state.session.actionCount++;
    
    return this.state;
  }

  // 批量更新
  batchUpdate(updates) {
    this.batchUpdates.push(updates);
    
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    
    this.batchTimeout = setTimeout(() => {
      const mergedUpdates = this.batchUpdates.reduce((acc, update) => {
        return this.deepMerge(acc, update);
      }, {});
      
      this.setState(mergedUpdates);
      this.batchUpdates = [];
      this.batchTimeout = null;
    }, 16); // 约等于一帧的时间
  }

  // 订阅状态变化
  subscribe(path, callback) {
    const id = Date.now() + Math.random();
    
    if (!this.subscribers.has(path)) {
      this.subscribers.set(path, new Map());
    }
    
    this.subscribers.get(path).set(id, callback);
    
    // 返回取消订阅函数
    return () => {
      const pathSubscribers = this.subscribers.get(path);
      if (pathSubscribers) {
        pathSubscribers.delete(id);
        if (pathSubscribers.size === 0) {
          this.subscribers.delete(path);
        }
      }
    };
  }

  // 通知订阅者
  notifySubscribers(updates) {
    const changedPaths = this.getChangedPaths(updates);
    
    changedPaths.forEach(path => {
      const pathSubscribers = this.subscribers.get(path);
      if (pathSubscribers) {
        const value = this.getState(path);
        pathSubscribers.forEach(callback => {
          try {
            callback(value, path);
          } catch (error) {
            console.error('Subscriber callback error:', error);
          }
        });
      }
    });
    
    // 通知通配符订阅者
    const wildcardSubscribers = this.subscribers.get('*');
    if (wildcardSubscribers) {
      wildcardSubscribers.forEach(callback => {
        try {
          callback(this.state, updates);
        } catch (error) {
          console.error('Wildcard subscriber callback error:', error);
        }
      });
    }
  }

  // 获取变更的路径
  getChangedPaths(updates, prefix = '') {
    const paths = [];
    
    Object.keys(updates).forEach(key => {
      const path = prefix ? `${prefix}.${key}` : key;
      paths.push(path);
      
      if (typeof updates[key] === 'object' && updates[key] !== null) {
        paths.push(...this.getChangedPaths(updates[key], path));
      }
    });
    
    return paths;
  }

  // 保存历史记录
  saveHistory() {
    const snapshot = {
      state: this.deepClone(this.state),
      timestamp: Date.now()
    };
    
    this.history.push(snapshot);
    
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  // 撤销操作
  undo() {
    if (this.history.length > 0) {
      const snapshot = this.history.pop();
      this.state = snapshot.state;
      this.notifySubscribers(this.state);
      return true;
    }
    return false;
  }

  // 重置状态
  reset(preserveSettings = true) {
    const settings = preserveSettings ? this.state.settings : null;
    
    this.state = {
      tabs: new Map(),
      windows: new Map(),
      settings: settings,
      ui: {
        activeTab: null,
        activeWindow: null,
        searchQuery: '',
        filters: {},
        sortBy: 'lastAccessed'
      },
      session: {
        startTime: Date.now(),
        lastActivity: Date.now(),
        actionCount: 0
      }
    };
    
    this.history = [];
    this.notifySubscribers(this.state);
  }

  // 深度克隆
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Map) return new Map(Array.from(obj.entries()).map(([k, v]) => [k, this.deepClone(v)]));
    if (obj instanceof Set) return new Set(Array.from(obj).map(v => this.deepClone(v)));
    if (obj instanceof Array) return obj.map(v => this.deepClone(v));
    
    const cloned = {};
    Object.keys(obj).forEach(key => {
      cloned[key] = this.deepClone(obj[key]);
    });
    return cloned;
  }

  // 深度合并
  deepMerge(target, source) {
    const result = this.deepClone(target);
    
    Object.keys(source).forEach(key => {
      if (source[key] === null || source[key] === undefined) {
        result[key] = source[key];
      } else if (typeof source[key] === 'object' && !Array.isArray(source[key]) && !(source[key] instanceof Map) && !(source[key] instanceof Set)) {
        if (result[key] && typeof result[key] === 'object') {
          result[key] = this.deepMerge(result[key], source[key]);
        } else {
          result[key] = this.deepClone(source[key]);
        }
      } else {
        result[key] = this.deepClone(source[key]);
      }
    });
    
    return result;
  }

  // 持久化状态
  async persist() {
    const persistableState = {
      tabs: Array.from(this.state.tabs.entries()),
      windows: Array.from(this.state.windows.entries()),
      settings: this.state.settings?.toStorage(),
      ui: this.state.ui,
      session: this.state.session
    };
    
    await chrome.storage.local.set({ globalState: persistableState });
  }

  // 恢复状态
  async restore() {
    const { globalState } = await chrome.storage.local.get('globalState');
    
    if (globalState) {
      this.state = {
        tabs: new Map(globalState.tabs),
        windows: new Map(globalState.windows),
        settings: globalState.settings ? new SettingsModel(globalState.settings) : null,
        ui: globalState.ui || this.state.ui,
        session: globalState.session || this.state.session
      };
      
      this.notifySubscribers(this.state);
    }
  }
}

// 创建全局状态管理器实例
export const stateManager = new StateManager();
```

### 4.2 数据流向说明

```javascript
// 数据流向示例
/*
1. 用户交互 (Popup/Options/Content Script)
   ↓
2. 发送消息到Background Script
   ↓
3. Message Handler处理消息
   ↓
4. 调用相应的Service (TabManager/WindowManager/etc.)
   ↓
5. Service执行操作并更新State Manager
   ↓
6. State Manager通知订阅者
   ↓
7. UI组件更新显示
*/

// 示例：Tab创建流程
export class TabCreationFlow {
  static async createTab(url, options = {}) {
    try {
      // 1. 验证输入
      if (!url || !TabModel.prototype.isValidUrl(url)) {
        throw new Error('Invalid URL');
      }
      
      // 2. 创建Chrome标签
      const chromeTab = await chrome.tabs.create({
        url,
        active: options.active !== false,
        windowId: options.windowId,
        index: options.index,
        pinned: options.pinned || false
      });
      
      // 3. 创建Tab模型
      const tabModel = TabModel.fromChromeTab(chromeTab);
      
      // 4. 更新状态
      stateManager.setState({
        tabs: new Map(stateManager.getState('tabs')).set(tabModel.id, tabModel)
      });
      
      // 5. 触发事件
      globalEventBus.emit(Events.TAB_CREATED, tabModel);
      
      // 6. 持久化
      await stateManager.persist();
      
      return tabModel;
    } catch (error) {
      // 7. 错误处理
      globalEventBus.emit(Events.ERROR_OCCURRED, {
        type: 'TAB_CREATION_ERROR',
        error,
        context: { url, options }
      });
      throw error;
    }
  }
}
```

### 4.3 状态更新机制

```javascript
// src/background/services/state-sync.js
export class StateSync {
  constructor(stateManager) {
    this.stateManager = stateManager;
    this.syncInterval = 5000; // 5秒同步一次
    this.syncTimer = null;
    this.pendingChanges = new Set();
  }

  // 启动同步
  start() {
    // 监听Chrome API事件
    this.setupChromeListeners();
    
    // 定期同步
    this.syncTimer = setInterval(() => {
      this.syncWithChrome();
    }, this.syncInterval);
    
    // 初始同步
    this.syncWithChrome();
  }

  // 停止同步
  stop() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  // 设置Chrome事件监听器
  setupChromeListeners() {
    // Tab事件
    chrome.tabs.onCreated.addListener(tab => {
      this.handleTabCreated(tab);
    });
    
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });
    
    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.handleTabRemoved(tabId, removeInfo);
    });
    
    chrome.tabs.onActivated.addListener(activeInfo => {
      this.handleTabActivated(activeInfo);
    });
    
    chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
      this.handleTabMoved(tabId, moveInfo);
    });
    
    // Window事件
    chrome.windows.onCreated.addListener(window => {
      this.handleWindowCreated(window);
    });
    
    chrome.windows.onRemoved.addListener(windowId => {
      this.handleWindowRemoved(windowId);
    });
    
    chrome.windows.onFocusChanged.addListener(windowId => {
      this.handleWindowFocusChanged(windowId);
    });
    
    // Storage事件
    chrome.storage.onChanged.addListener((changes, areaName) => {
      this.handleStorageChanged(changes, areaName);
    });
  }

  // 处理Tab创建
  handleTabCreated(tab) {
    const tabModel = TabModel.fromChromeTab(tab);
    const tabs = new Map(this.stateManager.getState('tabs'));
    tabs.set(tabModel.id, tabModel);
    
    this.stateManager.setState({ tabs }, { batch: true });
    this.pendingChanges.add('tabs');
  }

  // 处理Tab更新
  handleTabUpdated(tabId, changeInfo, tab) {
    const tabs = new Map(this.stateManager.getState('tabs'));
    const existingTab = tabs.get(tabId);
    
    if (existingTab) {
      const updatedTab = TabModel.fromChromeTab(tab);
      updatedTab.customData = existingTab.customData; // 保留自定义数据
      tabs.set(tabId, updatedTab);
      
      this.stateManager.setState({ tabs }, { batch: true });
      this.pendingChanges.add('tabs');
    }
  }

  // 处理Tab删除
  handleTabRemoved(tabId, removeInfo) {
    const tabs = new Map(this.stateManager.getState('tabs'));
    tabs.delete(tabId);
    
    // 更新窗口的标签列表
    const windows = new Map(this.stateManager.getState('windows'));
    const window = windows.get(removeInfo.windowId);
    if (window) {
      window.removeTab(tabId);
      windows.set(removeInfo.windowId, window);
    }
    
    this.stateManager.setState({ tabs, windows }, { batch: true });
    this.pendingChanges.add('tabs');
    this.pendingChanges.add('windows');
  }

  // 处理Tab激活
  handleTabActivated(activeInfo) {
    const { tabId, windowId } = activeInfo;
    
    this.stateManager.setState({
      ui: {
        ...this.stateManager.getState('ui'),
        activeTab: tabId,
        activeWindow: windowId
      }
    }, { batch: true });
    
    this.pendingChanges.add('ui');
  }

  // 处理Tab移动
  handleTabMoved(tabId, moveInfo) {
    const windows = new Map(this.stateManager.getState('windows'));
    const window = windows.get(moveInfo.windowId);
    
    if (window) {
      window.moveTab(tabId, moveInfo.toIndex);
      windows.set(moveInfo.windowId, window);
      
      this.stateManager.setState({ windows }, { batch: true });
      this.pendingChanges.add('windows');
    }
  }

  // 处理Window创建
  handleWindowCreated(window) {
    const windowModel = WindowModel.fromChromeWindow(window);
    const windows = new Map(this.stateManager.getState('windows'));
    windows.set(windowModel.id, windowModel);
    
    this.stateManager.setState({ windows }, { batch: true });
    this.pendingChanges.add('windows');
  }

  // 处理Window删除
  handleWindowRemoved(windowId) {
    const windows = new Map(this.stateManager.getState('windows'));
    const tabs = new Map(this.stateManager.getState('tabs'));
    
    // 删除窗口中的所有标签
    const window = windows.get(windowId);
    if (window) {
      window.tabs.forEach(tabId => tabs.delete(tabId));
    }
    
    windows.delete(windowId);
    
    this.stateManager.setState({ windows, tabs }, { batch: true });
    this.pendingChanges.add('windows');
    this.pendingChanges.add('tabs');
  }

  // 处理Window焦点变化
  handleWindowFocusChanged(windowId) {
    if (windowId !== chrome.windows.WINDOW_ID_NONE) {
      this.stateManager.setState({
        ui: {
          ...this.stateManager.getState('ui'),
          activeWindow: windowId
        }
      }, { batch: true });
      
      this.pendingChanges.add('ui');
    }
  }

  // 处理Storage变化
  handleStorageChanged(changes, areaName) {
    if (areaName === 'local' && changes.settings) {
      const newSettings = new SettingsModel(changes.settings.newValue);
      this.stateManager.setState({ settings: newSettings });
      this.pendingChanges.add('settings');
    }
  }

  // 与Chrome API同步
  async syncWithChrome() {
    try {
      // 获取所有窗口和标签
      const chromeWindows = await chrome.windows.getAll({ populate: true });
      const windows = new Map();
      const tabs = new Map();
      
      chromeWindows.forEach(chromeWindow => {
        const windowModel = WindowModel.fromChromeWindow(chromeWindow);
        windows.set(windowModel.id, windowModel);
        
        if (chromeWindow.tabs) {
          chromeWindow.tabs.forEach(chromeTab => {
            const tabModel = TabModel.fromChromeTab(chromeTab);
            tabs.set(tabModel.id, tabModel);
          });
        }
      });
      
      // 保留自定义数据
      const currentTabs = this.stateManager.getState('tabs');
      tabs.forEach((tab, id) => {
        const currentTab = currentTabs.get(id);
        if (currentTab && currentTab.customData) {
          tab.customData = currentTab.customData;
        }
      });
      
      // 更新状态
      this.stateManager.setState({ windows, tabs });
      
      // 清空待处理的变更
      this.pendingChanges.clear();
      
      // 持久化
      await this.stateManager.persist();
    } catch (error) {
      console.error('State sync error:', error);
      globalEventBus.emit(Events.ERROR_OCCURRED, {
        type: 'STATE_SYNC_ERROR',
        error
      });
    }
  }
}
```

## 5. 错误处理规范

### 5.1 统一的错误处理机制

```javascript
// src/shared/utils/error-handler.js
export class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.errorListeners = new Set();
    this.errorCodes = {
      // 系统错误
      SYSTEM_ERROR: 'SYSTEM_ERROR',
      NETWORK_ERROR: 'NETWORK_ERROR',
      PERMISSION_ERROR: 'PERMISSION_ERROR',
      
      // 数据错误
      VALIDATION_ERROR: 'VALIDATION_ERROR',
      STORAGE_ERROR: 'STORAGE_ERROR',
      SYNC_ERROR: 'SYNC_ERROR',
      
      // 操作错误
      TAB_NOT_FOUND: 'TAB_NOT_FOUND',
      WINDOW_NOT_FOUND: 'WINDOW_NOT_FOUND',
      INVALID_OPERATION: 'INVALID_OPERATION',
      
      // 用户错误
      USER_CANCELLED: 'USER_CANCELLED',
      INVALID_INPUT: 'INVALID_INPUT',
      QUOTA_EXCEEDED: 'QUOTA_EXCEEDED'
    };
  }

  // 处理错误
  handle(error, context = {}) {
    const errorInfo = this.normalizeError(error, context);
    
    // 添加到错误队列
    this.addToQueue(errorInfo);
    
    // 记录错误
    this.logError(errorInfo);
    
    // 通知监听器
    this.notifyListeners(errorInfo);
    
    // 发送到事件总线
    globalEventBus.emit(Events.ERROR_OCCURRED, errorInfo);
    
    return errorInfo;
  }

  // 标准化错误信息
  normalizeError(error, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      code: error.code || this.errorCodes.SYSTEM_ERROR,
      message: error.message || 'Unknown error',
      stack: error.stack,
      context: context,
      severity: this.determineSeverity(error),
      handled: false
    };
    
    // 特殊错误类型处理
    if (error instanceof TypeError) {
      errorInfo.code = this.errorCodes.VALIDATION_ERROR;
      errorInfo.severity = 'warning';
    } else if (error.name === 'QuotaExceededError') {
      errorInfo.code = this.errorCodes.QUOTA_EXCEEDED;
      errorInfo.severity = 'error';
    } else if (error.message?.includes('permission')) {
      errorInfo.code = this.errorCodes.PERMISSION_ERROR;
      errorInfo.severity = 'error';
    }
    
    return errorInfo;
  }

  // 确定错误严重程度
  determineSeverity(error) {
    if (error.severity) return error.severity;
    
    if (error.code === this.errorCodes.USER_CANCELLED) {
      return 'info';
    } else if (error.code === this.errorCodes.VALIDATION_ERROR || 
               error.code === this.errorCodes.INVALID_INPUT) {
      return 'warning';
    } else {
      return 'error';
    }
  }

  // 生成错误ID
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 添加到错误队列
  addToQueue(errorInfo) {
    this.errorQueue.push(errorInfo);
    
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  // 记录错误
  logError(errorInfo) {
    const logLevel = {
      info: console.info,
      warning: console.warn,
      error: console.error
    }[errorInfo.severity] || console.error;
    
    logLevel(`[${errorInfo.code}] ${errorInfo.message}`, {
      errorInfo,
      timestamp: new Date(errorInfo.timestamp).toISOString()
    });
    
    // 开发模式下打印堆栈
    if (process.env.NODE_ENV === 'development' && errorInfo.stack) {
      console.error(errorInfo.stack);
    }
  }

  // 添加错误监听器
  addListener(listener) {
    this.errorListeners.add(listener);
    return () => this.errorListeners.delete(listener);
  }

  // 通知监听器
  notifyListeners(errorInfo) {
    this.errorListeners.forEach(listener => {
      try {
        listener(errorInfo);
      } catch (err) {
        console.error('Error in error listener:', err);
      }
    });
  }

  // 获取错误历史
  getHistory(filter = {}) {
    let errors = [...this.errorQueue];
    
    if (filter.code) {
      errors = errors.filter(e => e.code === filter.code);
    }
    
    if (filter.severity) {
      errors = errors.filter(e => e.severity === filter.severity);
    }
    
    if (filter.since) {
      errors = errors.filter(e => e.timestamp >= filter.since);
    }
    
    return errors;
  }

  // 清除错误历史
  clearHistory() {
    this.errorQueue = [];
  }

  // 标记错误已处理
  markAsHandled(errorId) {
    const error = this.errorQueue.find(e => e.id === errorId);
    if (error) {
      error.handled = true;
    }
  }

  // 创建用户友好的错误消息
  getUserMessage(error) {
    const messages = {
      [this.errorCodes.NETWORK_ERROR]: '网络连接失败，请检查您的网络设置',
      [this.errorCodes.PERMISSION_ERROR]: '权限不足，请检查扩展权限设置',
      [this.errorCodes.STORAGE_ERROR]: '存储操作失败，可能是存储空间不足',
      [this.errorCodes.TAB_NOT_FOUND]: '标签页未找到，可能已被关闭',
      [this.errorCodes.WINDOW_NOT_FOUND]: '窗口未找到，可能已被关闭',
      [this.errorCodes.QUOTA_EXCEEDED]: '已达到存储限制，请清理一些数据',
      [this.errorCodes.INVALID_INPUT]: '输入无效，请检查您的输入',
      [this.errorCodes.USER_CANCELLED]: '操作已取消'
    };
    
    return messages[error.code] || error.message || '发生了未知错误';
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler();

// 全局错误捕获
window.addEventListener('error', (event) => {
  errorHandler.handle(event.error, {
    type: 'uncaught',
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handle(event.reason, {
    type: 'unhandled_promise',
    promise: event.promise
  });
});
```

### 5.2 错误恢复机制

```javascript
// src/shared/utils/error-recovery.js
export class ErrorRecovery {
  constructor() {
    this.recoveryStrategies = new Map();
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    };
    
    this.setupDefaultStrategies();
  }

  // 设置默认恢复策略
  setupDefaultStrategies() {
    // 网络错误恢复
    this.register(errorHandler.errorCodes.NETWORK_ERROR, async (error, retry) => {
      if (retry < this.retryConfig.maxRetries) {
        const delay = this.calculateDelay(retry);
        await this.delay(delay);
        return { retry: true, delay };
      }
      return { retry: false, fallback: 'offline_mode' };
    });
    
    // 存储错误恢复
    this.register(errorHandler.errorCodes.STORAGE_ERROR, async (error) => {
      // 尝试清理缓存
      const cleaned = await this.cleanupStorage();
      if (cleaned > 0) {
        return { retry: true, cleaned };
      }
      return { retry: false, fallback: 'memory_only' };
    });
    
    // 权限错误恢复
    this.register(errorHandler.errorCodes.PERMISSION_ERROR, async (error) => {
      // 请求权限
      const granted = await this.requestPermissions(error.context.permissions);
      return { retry: granted, granted };
    });
    
    // 配额超出恢复
    this.register(errorHandler.errorCodes.QUOTA_EXCEEDED, async (error) => {
      // 清理旧数据
      const freed = await this.freeUpSpace();
      return { retry: freed > 0, freed };
    });
  }

  // 注册恢复策略
  register(errorCode, strategy) {
    this.recoveryStrategies.set(errorCode, strategy);
  }

  // 尝试恢复
  async tryRecover(error, retryCount = 0) {
    const strategy = this.recoveryStrategies.get(error.code);
    if (!strategy) {
      return { success: false, reason: 'no_strategy' };
    }
    
    try {
      const result = await strategy(error, retryCount);
      
      if (result.retry) {
        globalEventBus.emit(Events.ERROR_RESOLVED, {
          error,
          recovery: result,
          retryCount
        });
      }
      
      return {
        success: result.retry,
        result,
        retryCount
      };
    } catch (recoveryError) {
      errorHandler.handle(recoveryError, {
        originalError: error,
        recoveryAttempt: retryCount
      });
      
      return {
        success: false,
        reason: 'recovery_failed',
        error: recoveryError
      };
    }
  }

  // 计算重试延迟
  calculateDelay(retryCount) {
    const delay = Math.min(
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, retryCount),
      this.retryConfig.maxDelay
    );
    
    // 添加随机抖动
    return delay + Math.random() * 1000;
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 清理存储空间
  async cleanupStorage() {
    let freed = 0;
    
    try {
      // 清理过期数据
      const { expiredData } = await chrome.storage.local.get('expiredData');
      if (expiredData) {
        await chrome.storage.local.remove(Object.keys(expiredData));
        freed += Object.keys(expiredData).length;
      }
      
      // 清理缓存
      const cache = await chrome.storage.local.get(null);
      const cacheKeys = Object.keys(cache).filter(key => key.startsWith('cache_'));
      if (cacheKeys.length > 100) {
        const toRemove = cacheKeys.slice(0, 50);
        await chrome.storage.local.remove(toRemove);
        freed += toRemove.length;
      }
    } catch (error) {
      console.error('Storage cleanup error:', error);
    }
    
    return freed;
  }

  // 请求权限
  async requestPermissions(permissions) {
    try {
      return await chrome.permissions.request({
        permissions: permissions || []
      });
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  }

  // 释放空间
  async freeUpSpace() {
    let freed = 0;
    
    try {
      // 清理历史记录
      const oldHistory = stateManager.history.splice(0, Math.floor(stateManager.history.length / 2));
      freed += oldHistory.length;
      
      // 清理错误日志
      const oldErrors = errorHandler.errorQueue.length;
      errorHandler.clearHistory();
      freed += oldErrors;
      
      // 触发垃圾回收
      if (global.gc) {
        global.gc();
      }
    } catch (error) {
      console.error('Free up space error:', error);
    }
    
    return freed;
  }

  // 带重试的执行函数
  async executeWithRetry(fn, options = {}) {
    const {
      maxRetries = this.retryConfig.maxRetries,
      onRetry = () => {},
      shouldRetry = (error) => true
    } = options;
    
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i === maxRetries || !shouldRetry(error)) {
          throw error;
        }
        
        const delay = this.calculateDelay(i);
        onRetry(error, i + 1, delay);
        await this.delay(delay);
      }
    }
    
    throw lastError;
  }
}

// 创建全局错误恢复实例
export const errorRecovery = new ErrorRecovery();
```

### 5.3 用户错误提示规范

```javascript
// src/shared/utils/notification-manager.js
export class NotificationManager {
  constructor() {
    this.notificationQueue = [];
    this.isProcessing = false;
    this.defaultDuration = 5000;
    this.maxConcurrent = 3;
    this.activeNotifications = new Set();
  }

  // 显示错误通知
  async showError(error, options = {}) {
    const {
      duration = this.defaultDuration,
      action = null,
      persistent = false
    } = options;
    
    const notification = {
      id: this.generateId(),
      type: 'error',
      title: '错误',
      message: errorHandler.getUserMessage(error),
      icon: '/assets/icons/error.png',
      duration,
      action,
      persistent,
      timestamp: Date.now()
    };
    
    return await this.show(notification);
  }

  // 显示成功通知
  async showSuccess(message, options = {}) {
    const notification = {
      id: this.generateId(),
      type: 'success',
      title: '成功',
      message,
      icon: '/assets/icons/success.png',
      duration: options.duration || 3000,
      timestamp: Date.now()
    };
    
    return await this.show(notification);
  }

  // 显示警告通知
  async showWarning(message, options = {}) {
    const notification = {
      id: this.generateId(),
      type: 'warning',
      title: '警告',
      message,
      icon: '/assets/icons/warning.png',
      duration: options.duration || this.defaultDuration,
      action: options.action,
      timestamp: Date.now()
    };
    
    return await this.show(notification);
  }

  // 显示信息通知
  async showInfo(message, options = {}) {
    const notification = {
      id: this.generateId(),
      type: 'info',
      title: '提示',
      message,
      icon: '/assets/icons/info.png',
      duration: options.duration || this.defaultDuration,
      timestamp: Date.now()
    };
    
    return await this.show(notification);
  }

  // 显示通知
  async show(notification) {
    // 添加到队列
    this.notificationQueue.push(notification);
    
    // 处理队列
    if (!this.isProcessing) {
      await this.processQueue();
    }
    
    return notification.id;
  }

  // 处理通知队列
  async processQueue() {
    this.isProcessing = true;
    
    while (this.notificationQueue.length > 0 && this.activeNotifications.size < this.maxConcurrent) {
      const notification = this.notificationQueue.shift();
      await this.displayNotification(notification);
    }
    
    this.isProcessing = false;
  }

  // 显示单个通知
  async displayNotification(notification) {
    this.activeNotifications.add(notification.id);
    
    try {
      if (chrome.notifications) {
        // 使用Chrome通知API
        await chrome.notifications.create(notification.id, {
          type: 'basic',
          iconUrl: notification.icon,
          title: notification.title,
          message: notification.message,
          priority: notification.type === 'error' ? 2 : 1,
          buttons: notification.action ? [{
            title: notification.action.label
          }] : undefined,
          requireInteraction: notification.persistent
        });
        
        // 设置自动关闭
        if (!notification.persistent && notification.duration > 0) {
          setTimeout(() => {
            this.close(notification.id);
          }, notification.duration);
        }
        
        // 监听按钮点击
        if (notification.action) {
          chrome.notifications.onButtonClicked.addListener((notifId, buttonIndex) => {
            if (notifId === notification.id && buttonIndex === 0) {
              notification.action.callback();
              this.close(notification.id);
            }
          });
        }
      } else {
        // 降级到控制台
        console[notification.type](`[${notification.title}] ${notification.message}`);
        this.activeNotifications.delete(notification.id);
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
      this.activeNotifications.delete(notification.id);
    }
    
    // 继续处理队列
    if (this.notificationQueue.length > 0) {
      setTimeout(() => this.processQueue(), 500);
    }
  }

  // 关闭通知
  async close(notificationId) {
    try {
      if (chrome.notifications) {
        await chrome.notifications.clear(notificationId);
      }
    } catch (error) {
      console.error('Failed to close notification:', error);
    } finally {
      this.activeNotifications.delete(notificationId);
    }
  }

  // 清除所有通知
  async clearAll() {
    const promises = Array.from(this.activeNotifications).map(id => this.close(id));
    await Promise.all(promises);
    this.notificationQueue = [];
  }

  // 生成通知ID
  generateId() {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 创建错误报告
  createErrorReport(error) {
    const report = {
      error: errorHandler.normalizeError(error),
      system: {
        version: chrome.runtime.getManifest().version,
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language
      },
      state: {
        tabCount: stateManager.getState('tabs').size,
        windowCount: stateManager.getState('windows').size,
        memoryUsage: performance.memory ? {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        } : null
      },
      timestamp: Date.now()
    };
    
    return report;
  }
}

// 创建全局通知管理器实例
export const notificationManager = new NotificationManager();

// 集成错误处理器
errorHandler.addListener((error) => {
  if (error.severity === 'error' && !error.silent) {
    notificationManager.showError(error, {
      action: error.code === errorHandler.errorCodes.NETWORK_ERROR ? {
        label: '重试',
        callback: () => {
          errorRecovery.tryRecover(error);
        }
      } : null
    });
  }
});
```

## 6. 性能要求

### 6.1 性能指标定义

```javascript
// src/shared/utils/performance-monitor.js
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.thresholds = {
      // 数据加载性能指标 (毫秒)
      tabLoad: 50,          // 单个标签加载
      windowLoad: 100,      // 单个窗口加载
      fullSync: 500,        // 完整同步
      search: 100,          // 搜索响应
      
      // 内存使用限制 (MB)
      maxMemory: 50,        // 最大内存使用
      warningMemory: 40,    // 警告阈值
      
      // 响应时间要求 (毫秒)
      uiResponse: 100,      // UI响应
      apiCall: 200,         // API调用
      storage: 50,          // 存储操作
      
      // 其他指标
      maxTabs: 1000,        // 最大标签数
      maxWindows: 50,       // 最大窗口数
      fps: 60               // 帧率
    };
    
    this.setupMonitoring();
  }

  // 设置性能监控
  setupMonitoring() {
    // 监控内存使用
    if (performance.memory) {
      setInterval(() => {
        this.checkMemoryUsage();
      }, 10000); // 每10秒检查一次
    }
    
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            this.recordMetric('longTask', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
          }
        }
      });
      
      observer.observe({ entryTypes: ['longtask'] });
    }
  }

  // 开始计时
  startTimer(name) {
    const timer = {
      name,
      startTime: performance.now(),
      marks: []
    };
    
    this.metrics.set(name, timer);
    return timer;
  }

  // 结束计时
  endTimer(name) {
    const timer = this.metrics.get(name);
    if (!timer) return null;
    
    const duration = performance.now() - timer.startTime;
    timer.duration = duration;
    timer.endTime = performance.now();
    
    // 检查是否超过阈值
    this.checkThreshold(name, duration);
    
    return {
      name,
      duration,
      marks: timer.marks
    };
  }

  // 添加标记
  mark(timerName, markName) {
    const timer = this.metrics.get(timerName);
    if (timer) {
      timer.marks.push({
        name: markName,
        time: performance.now() - timer.startTime
      });
    }
  }

  // 记录指标
  recordMetric(name, value) {
    const metric = {
      name,
      value,
      timestamp: Date.now()
    };
    
    // 存储到metrics
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metricList = this.metrics.get(name);
    metricList.push(metric);
    
    // 保持列表大小
    if (metricList.length > 1000) {
      metricList.shift();
    }
    
    return metric;
  }

  // 检查阈值
  checkThreshold(name, value) {
    const threshold = this.thresholds[name];
    if (threshold && value > threshold) {
      console.warn(`Performance threshold exceeded for ${name}: ${value}ms (threshold: ${threshold}ms)`);
      
      globalEventBus.emit('performance:threshold_exceeded', {
        metric: name,
        value,
        threshold,
        exceeded: value - threshold
      });
    }
  }

  // 检查内存使用
  checkMemoryUsage() {
    if (!performance.memory) return;
    
    const usedMemoryMB = performance.memory.usedJSHeapSize / (1024 * 1024);
    
    this.recordMetric('memoryUsage', usedMemoryMB);
    
    if (usedMemoryMB > this.thresholds.maxMemory) {
      globalEventBus.emit('performance:memory_exceeded', {
        used: usedMemoryMB,
        limit: this.thresholds.maxMemory
      });
    } else if (usedMemoryMB > this.thresholds.warningMemory) {
      globalEventBus.emit('performance:memory_warning', {
        used: usedMemoryMB,
        warning: this.thresholds.warningMemory
      });
    }
  }

  // 获取性能报告
  getReport() {
    const report = {
      timestamp: Date.now(),
      metrics: {},
      summary: {}
    };
    
    // 收集所有指标
    this.metrics.forEach((value, key) => {
      if (Array.isArray(value)) {
        // 计算统计信息
        const values = value.map(m => m.value);
        report.metrics[key] = {
          count: values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          latest: values[values.length - 1]
        };
      } else if (value.duration !== undefined) {
        report.metrics[key] = {
          duration: value.duration,
          marks: value.marks
        };
      }
    });
    
    // 生成摘要
    report.summary = {
      memoryUsage: performance.memory ? {
        used: performance.memory.usedJSHeapSize / (1024 * 1024),
        total: performance.memory.totalJSHeapSize / (1024 * 1024),
        limit: performance.memory.jsHeapSizeLimit / (1024 * 1024)
      } : null,
      tabCount: stateManager.getState('tabs').size,
      windowCount: stateManager.getState('windows').size,
      uptime: Date.now() - stateManager.getState('session.startTime')
    };
    
    return report;
  }

  // 清除指标
  clearMetrics() {
    this.metrics.clear();
  }

  // 性能优化建议
  getOptimizationSuggestions() {
    const suggestions = [];
    const report = this.getReport();
    
    // 内存优化建议
    if (report.summary.memoryUsage && report.summary.memoryUsage.used > this.thresholds.warningMemory) {
      suggestions.push({
        type: 'memory',
        severity: 'warning',
        message: '内存使用较高，建议清理不必要的数据',
        action: 'cleanupMemory'
      });
    }
    
    // 标签数优化建议
    if (report.summary.tabCount > 100) {
      suggestions.push({
        type: 'tabs',
        severity: 'info',
        message: '打开的标签页较多，可能影响性能',
        action: 'reduceTabs'
      });
    }
    
    // 响应时间优化建议
    Object.entries(report.metrics).forEach(([key, value]) => {
      if (value.avg && this.thresholds[key] && value.avg > this.thresholds[key]) {
        suggestions.push({
          type: 'performance',
          severity: 'warning',
          message: `${key}平均响应时间过长(${value.avg.toFixed(2)}ms)`,
          action: 'optimize',
          metric: key
        });
      }
    });
    
    return suggestions;
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 性能优化工具
export class PerformanceOptimizer {
  // 防抖函数
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // 节流函数
  static throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // 懒加载
  static lazyLoad(loader) {
    let cache;
    return async function() {
      if (!cache) {
        cache = await loader();
      }
      return cache;
    };
  }

  // 批处理
  static batchProcess(items, batchSize, processor) {
    return new Promise(async (resolve, reject) => {
      const results = [];
      
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        
        try {
          const batchResults = await Promise.all(
            batch.map(item => processor(item))
          );
          results.push(...batchResults);
          
          // 让出主线程
          await new Promise(resolve => setTimeout(resolve, 0));
        } catch (error) {
          reject(error);
          return;
        }
      }
      
      resolve(results);
    });
  }

  // 虚拟滚动
  static virtualScroll(container, items, itemHeight, renderItem) {
    const visibleCount = Math.ceil(container.clientHeight / itemHeight);
    const totalHeight = items.length * itemHeight;
    let scrollTop = 0;
    
    const render = () => {
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(startIndex + visibleCount + 1, items.length);
      
      const visibleItems = items.slice(startIndex, endIndex);
      const offsetY = startIndex * itemHeight;
      
      // 清空容器
      container.innerHTML = '';
      
      // 创建占位元素
      const spacer = document.createElement('div');
      spacer.style.height = `${totalHeight}px`;
      container.appendChild(spacer);
      
      // 渲染可见项
      const itemsContainer = document.createElement('div');
      itemsContainer.style.transform = `translateY(${offsetY}px)`;
      itemsContainer.style.position = 'absolute';
      itemsContainer.style.top = '0';
      itemsContainer.style.left = '0';
      itemsContainer.style.right = '0';
      
      visibleItems.forEach((item, index) => {
        const element = renderItem(item, startIndex + index);
        itemsContainer.appendChild(element);
      });
      
      container.appendChild(itemsContainer);
    };
    
    container.addEventListener('scroll', () => {
      scrollTop = container.scrollTop;
      requestAnimationFrame(render);
    });
    
    render();
  }
}

// 内存管理工具
export class MemoryManager {
  static cleanup() {
    // 清理过期数据
    const now = Date.now();
    const tabs = stateManager.getState('tabs');
    const expiredTabs = [];
    
    tabs.forEach((tab, id) => {
      if (now - tab.lastAccessed > 3600000) { // 1小时未访问
        expiredTabs.push(id);
      }
    });
    
    // 清理错误日志
    if (errorHandler.errorQueue.length > 50) {
      errorHandler.errorQueue = errorHandler.errorQueue.slice(-50);
    }
    
    // 清理性能指标
    performanceMonitor.metrics.forEach((value, key) => {
      if (Array.isArray(value) && value.length > 100) {
        performanceMonitor.metrics.set(key, value.slice(-100));
      }
    });
    
    // 触发垃圾回收
    if (global.gc) {
      global.gc();
    }
    
    return {
      expiredTabs: expiredTabs.length,
      timestamp: Date.now()
    };
  }

  static getMemoryInfo() {
    if (!performance.memory) {
      return null;
    }
    
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      usage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
    };
  }
}
```

## 总结

本文档定义了Chrome扩展基础框架的完整需求，包括：

1. **项目架构**：清晰的文件结构、详细的manifest.json配置、模块依赖关系
2. **数据模型**：Tab、Window、Settings的完整定义及相互关系
3. **通信机制**：基于Chrome API的消息传递、事件总线、模块API
4. **状态管理**：集中式状态管理、数据流向、同步机制
5. **错误处理**：统一的错误处理、恢复机制、用户提示
6. **性能要求**：明确的性能指标、监控工具、优化方案

该框架设计遵循了模块化、可扩展、高性能的原则，能够支持复杂的Chrome扩展开发需求。