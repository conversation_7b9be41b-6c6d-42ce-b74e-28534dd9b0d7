// 窗口API接口实现
import { getStorage, setStorage } from '../utils/storage.js';
import { v4 as uuidv4 } from '../utils/uuid.js';

// 存储键
const WINDOWS_STORAGE_KEY = 'mytab_windows';
const TABS_STORAGE_KEY = 'mytab_tabs';

// 验证窗口数据
function validateWindowData(windowData) {
  if (!windowData.name || windowData.name.trim().length === 0) {
    throw new Error('Window name is required');
  }
  
  if (windowData.name.length > 100) {
    throw new Error('Window name must be less than 100 characters');
  }
  
  if (windowData.color && !/^#[0-9A-Fa-f]{6}$/.test(windowData.color)) {
    throw new Error('Invalid color format. Must be #RRGGBB');
  }
  
  if (windowData.position !== undefined && windowData.position < 0) {
    throw new Error('Position must be a non-negative integer');
  }
  
  return true;
}

// 创建新窗口
export async function createWindow(windowData) {
  try {
    // 验证输入数据
    validateWindowData(windowData);
    
    // 生成窗口ID
    const windowId = windowData.windowId || uuidv4();
    
    // 构建完整的窗口对象
    const window = {
      windowId,
      name: windowData.name,
      tabs: windowData.tabs || [],
      tabCount: (windowData.tabs || []).length,
      createdAt: windowData.createdAt || Date.now(),
      updatedAt: windowData.updatedAt || Date.now(),
      lastAccessedAt: windowData.lastAccessedAt || Date.now(),
      isCollapsed: windowData.isCollapsed || false,
      color: windowData.color || '#2563eb',
      icon: windowData.icon || 'default',
      position: windowData.position !== undefined ? windowData.position : 0,
      metadata: windowData.metadata || {}
    };
    
    // 获取现有窗口
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    
    // 如果没有指定位置，添加到末尾
    if (windowData.position === undefined) {
      window.position = windows.length;
    }
    
    // 添加新窗口
    windows.push(window);
    
    // 保存到存储
    await setStorage(WINDOWS_STORAGE_KEY, windows);
    
    return window;
  } catch (error) {
    console.error('Failed to create window:', error);
    throw error;
  }
}

// 更新窗口信息
export async function updateWindow(windowId, updates) {
  try {
    // 获取所有窗口
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const windowIndex = windows.findIndex(w => w.windowId === windowId);
    
    if (windowIndex === -1) {
      throw new Error(`Window not found: ${windowId}`);
    }
    
    // 验证更新数据
    if (updates.name !== undefined) {
      validateWindowData({ ...windows[windowIndex], name: updates.name });
    }
    if (updates.color !== undefined) {
      validateWindowData({ ...windows[windowIndex], color: updates.color });
    }
    
    // 更新窗口数据
    const updatedWindow = {
      ...windows[windowIndex],
      ...updates,
      updatedAt: Date.now()
    };
    
    // 确保关键字段不被覆盖
    updatedWindow.windowId = windowId;
    updatedWindow.createdAt = windows[windowIndex].createdAt;
    
    // 更新标签计数
    if (updates.tabs) {
      updatedWindow.tabCount = updates.tabs.length;
    }
    
    windows[windowIndex] = updatedWindow;
    
    // 保存更改
    await setStorage(WINDOWS_STORAGE_KEY, windows);
    
    return updatedWindow;
  } catch (error) {
    console.error('Failed to update window:', error);
    throw error;
  }
}

// 删除窗口
export async function deleteWindow(windowId) {
  try {
    // 获取所有窗口
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const windowIndex = windows.findIndex(w => w.windowId === windowId);
    
    if (windowIndex === -1) {
      throw new Error(`Window not found: ${windowId}`);
    }
    
    // 删除窗口
    windows.splice(windowIndex, 1);
    
    // 更新剩余窗口的位置
    windows.forEach((window, index) => {
      window.position = index;
    });
    
    // 保存更改
    await setStorage(WINDOWS_STORAGE_KEY, windows);
    
    return true;
  } catch (error) {
    console.error('Failed to delete window:', error);
    throw error;
  }
}

// 获取单个窗口
export async function getWindow(windowId) {
  try {
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const window = windows.find(w => w.windowId === windowId);
    
    if (!window) {
      throw new Error(`Window not found: ${windowId}`);
    }
    
    // 加载关联的标签页
    const allTabs = await getStorage(TABS_STORAGE_KEY) || [];
    window.tabs = allTabs.filter(tab => tab.windowId === windowId);
    window.tabCount = window.tabs.length;
    
    return window;
  } catch (error) {
    console.error('Failed to get window:', error);
    throw error;
  }
}

// 获取所有窗口
export async function getAllWindows() {
  try {
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const allTabs = await getStorage(TABS_STORAGE_KEY) || [];
    
    // 为每个窗口加载标签页
    const windowsWithTabs = windows.map(window => {
      const windowTabs = allTabs.filter(tab => tab.windowId === window.windowId);
      return {
        ...window,
        tabs: windowTabs,
        tabCount: windowTabs.length
      };
    });
    
    // 按position排序
    windowsWithTabs.sort((a, b) => a.position - b.position);
    
    return windowsWithTabs;
  } catch (error) {
    console.error('Failed to get all windows:', error);
    throw error;
  }
}

// 移动标签页到目标窗口
export async function moveTabToWindow(tabId, targetWindowId) {
  try {
    // 获取所有窗口和标签页
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    
    // 查找标签页
    const tabIndex = tabs.findIndex(t => t.id === tabId);
    if (tabIndex === -1) {
      throw new Error(`Tab not found: ${tabId}`);
    }
    
    // 查找目标窗口
    const targetWindow = windows.find(w => w.windowId === targetWindowId);
    if (!targetWindow) {
      throw new Error(`Target window not found: ${targetWindowId}`);
    }
    
    const tab = tabs[tabIndex];
    const oldWindowId = tab.windowId;
    
    // 更新标签页的windowId
    tab.windowId = targetWindowId;
    tab.updatedAt = Date.now();
    
    // 保存标签页更改
    await setStorage(TABS_STORAGE_KEY, tabs);
    
    // 更新窗口的lastAccessedAt
    const sourceWindowIndex = windows.findIndex(w => w.windowId === oldWindowId);
    const targetWindowIndex = windows.findIndex(w => w.windowId === targetWindowId);
    
    if (sourceWindowIndex !== -1) {
      windows[sourceWindowIndex].updatedAt = Date.now();
    }
    
    windows[targetWindowIndex].lastAccessedAt = Date.now();
    windows[targetWindowIndex].updatedAt = Date.now();
    
    // 保存窗口更改
    await setStorage(WINDOWS_STORAGE_KEY, windows);
    
    return true;
  } catch (error) {
    console.error('Failed to move tab to window:', error);
    throw error;
  }
}

// 合并窗口
export async function mergeWindows(sourceWindowId, targetWindowId) {
  try {
    // 获取两个窗口
    const sourceWindow = await getWindow(sourceWindowId);
    const targetWindow = await getWindow(targetWindowId);
    
    // 移动所有标签页
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    let movedCount = 0;
    
    tabs.forEach(tab => {
      if (tab.windowId === sourceWindowId) {
        tab.windowId = targetWindowId;
        tab.updatedAt = Date.now();
        movedCount++;
      }
    });
    
    // 保存标签页更改
    await setStorage(TABS_STORAGE_KEY, tabs);
    
    // 删除源窗口
    await deleteWindow(sourceWindowId);
    
    // 更新目标窗口
    const updatedTarget = await updateWindow(targetWindowId, {
      lastAccessedAt: Date.now()
    });
    
    return updatedTarget;
  } catch (error) {
    console.error('Failed to merge windows:', error);
    throw error;
  }
}

// 拆分窗口
export async function splitWindow(windowId, tabIds, newWindowName) {
  try {
    // 验证源窗口存在
    const sourceWindow = await getWindow(windowId);
    
    // 创建新窗口
    const newWindow = await createWindow({
      name: newWindowName || `从 ${sourceWindow.name} 拆分`,
      color: sourceWindow.color,
      icon: sourceWindow.icon,
      metadata: {
        splitFrom: windowId,
        splitAt: Date.now()
      }
    });
    
    // 移动指定的标签页到新窗口
    for (const tabId of tabIds) {
      await moveTabToWindow(tabId, newWindow.windowId);
    }
    
    // 更新源窗口
    await updateWindow(windowId, {
      lastAccessedAt: Date.now()
    });
    
    // 返回新窗口（包含移动的标签页）
    return await getWindow(newWindow.windowId);
  } catch (error) {
    console.error('Failed to split window:', error);
    throw error;
  }
}

// 批量操作辅助函数

// 批量更新窗口
export async function batchUpdateWindows(updates) {
  try {
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    const updateMap = new Map(updates.map(u => [u.windowId, u.updates]));
    
    windows.forEach(window => {
      const update = updateMap.get(window.windowId);
      if (update) {
        Object.assign(window, update, { updatedAt: Date.now() });
      }
    });
    
    await setStorage(WINDOWS_STORAGE_KEY, windows);
    return true;
  } catch (error) {
    console.error('Failed to batch update windows:', error);
    throw error;
  }
}

// 获取窗口中的标签页数量
export async function getWindowTabCount(windowId) {
  try {
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    return tabs.filter(tab => tab.windowId === windowId).length;
  } catch (error) {
    console.error('Failed to get window tab count:', error);
    throw error;
  }
}

// 验证窗口是否存在
export async function windowExists(windowId) {
  try {
    const windows = await getStorage(WINDOWS_STORAGE_KEY) || [];
    return windows.some(w => w.windowId === windowId);
  } catch (error) {
    console.error('Failed to check window existence:', error);
    return false;
  }
}