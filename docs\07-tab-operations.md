# 标签页操作模块详细需求文档

## 1. 模块概述

标签页操作模块是整个扩展的核心功能模块之一，负责处理所有与标签页相关的用户操作，并作为应用层与Chrome tabs API之间的桥梁。

### 主要职责
- 提供标签页的增删改查操作接口
- 管理标签页的生命周期
- 同步浏览器标签状态到本地存储
- 处理批量操作和性能优化
- 提供错误处理和恢复机制

### 技术架构
```javascript
// 模块结构
src/
  services/
    tabOperations.js      // 核心操作服务
    tabSync.js           // 状态同步服务
    sessionRestore.js    // 会话恢复服务
  utils/
    tabValidator.js      // 标签页验证工具
    batchProcessor.js    // 批量处理工具
```

## 2. 核心操作功能

### 2.1 打开标签页

#### openTab(tabData): Promise<ChromeTab>
打开单个标签页

```javascript
/**
 * 打开单个标签页
 * @param {Object} tabData - 标签页数据
 * @param {string} tabData.url - 标签页URL
 * @param {boolean} [tabData.active=true] - 是否激活
 * @param {number} [tabData.index] - 插入位置
 * @param {number} [tabData.windowId] - 目标窗口ID
 * @param {boolean} [tabData.pinned=false] - 是否固定
 * @returns {Promise<chrome.tabs.Tab>} 创建的标签页对象
 */
async function openTab(tabData) {
  // 验证URL
  const validatedUrl = validateUrl(tabData.url);
  
  // 构建创建参数
  const createProperties = {
    url: validatedUrl,
    active: tabData.active ?? true,
    pinned: tabData.pinned ?? false
  };
  
  if (tabData.index !== undefined) {
    createProperties.index = tabData.index;
  }
  
  if (tabData.windowId) {
    createProperties.windowId = tabData.windowId;
  }
  
  try {
    const tab = await chrome.tabs.create(createProperties);
    
    // 触发状态同步
    await syncTabCreated(tab);
    
    return tab;
  } catch (error) {
    console.error('Failed to open tab:', error);
    throw new TabOperationError('OPEN_FAILED', error.message);
  }
}
```

#### openTabs(tabDataArray): Promise<ChromeTab[]>
批量打开多个标签页

```javascript
/**
 * 批量打开标签页
 * @param {Array<Object>} tabDataArray - 标签页数据数组
 * @param {Object} options - 批量操作选项
 * @param {number} [options.batchSize=10] - 每批次大小
 * @param {number} [options.delay=100] - 批次间延迟(ms)
 * @param {Function} [options.onProgress] - 进度回调
 * @param {AbortSignal} [options.signal] - 取消信号
 * @returns {Promise<Array<chrome.tabs.Tab>>} 创建的标签页数组
 */
async function openTabs(tabDataArray, options = {}) {
  const {
    batchSize = 10,
    delay = 100,
    onProgress,
    signal
  } = options;
  
  const results = [];
  const errors = [];
  
  // 分批处理
  for (let i = 0; i < tabDataArray.length; i += batchSize) {
    // 检查取消信号
    if (signal?.aborted) {
      throw new AbortError('Operation cancelled');
    }
    
    const batch = tabDataArray.slice(i, i + batchSize);
    
    // 并行处理当前批次
    const batchResults = await Promise.allSettled(
      batch.map(tabData => openTab(tabData))
    );
    
    // 收集结果
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        errors.push({
          index: i + index,
          error: result.reason,
          tabData: batch[index]
        });
      }
    });
    
    // 进度回调
    if (onProgress) {
      onProgress({
        completed: results.length,
        failed: errors.length,
        total: tabDataArray.length,
        percentage: ((i + batch.length) / tabDataArray.length) * 100
      });
    }
    
    // 批次间延迟
    if (i + batchSize < tabDataArray.length) {
      await sleep(delay);
    }
  }
  
  // 返回结果和错误信息
  return {
    success: results,
    errors: errors
  };
}
```

#### openInNewWindow(tabDataArray): Promise<ChromeWindow>
在新窗口中打开标签页

```javascript
/**
 * 在新窗口中打开标签页
 * @param {Array<Object>} tabDataArray - 标签页数据数组
 * @param {Object} windowOptions - 窗口选项
 * @returns {Promise<chrome.windows.Window>} 创建的窗口对象
 */
async function openInNewWindow(tabDataArray, windowOptions = {}) {
  if (!tabDataArray.length) {
    throw new Error('No tabs to open');
  }
  
  // 创建窗口时打开第一个标签页
  const firstTab = tabDataArray[0];
  const window = await chrome.windows.create({
    url: firstTab.url,
    focused: windowOptions.focused ?? true,
    type: windowOptions.type ?? 'normal',
    state: windowOptions.state ?? 'normal',
    ...windowOptions
  });
  
  // 打开剩余标签页
  if (tabDataArray.length > 1) {
    const remainingTabs = tabDataArray.slice(1).map(tab => ({
      ...tab,
      windowId: window.id
    }));
    
    await openTabs(remainingTabs, {
      batchSize: 5,
      delay: 50
    });
  }
  
  return window;
}
```

### 2.2 更新标签页

#### updateTabInfo(tabId, updates): Promise<void>
更新标签页信息

```javascript
/**
 * 更新标签页信息
 * @param {number} tabId - 标签页ID
 * @param {Object} updates - 更新内容
 * @param {string} [updates.url] - 新URL
 * @param {boolean} [updates.active] - 是否激活
 * @param {boolean} [updates.pinned] - 是否固定
 * @param {boolean} [updates.muted] - 是否静音
 * @returns {Promise<void>}
 */
async function updateTabInfo(tabId, updates) {
  try {
    // 验证更新内容
    const validatedUpdates = {};
    
    if (updates.url) {
      validatedUpdates.url = validateUrl(updates.url);
    }
    
    ['active', 'pinned', 'muted'].forEach(prop => {
      if (updates[prop] !== undefined) {
        validatedUpdates[prop] = updates[prop];
      }
    });
    
    // 执行更新
    const updatedTab = await chrome.tabs.update(tabId, validatedUpdates);
    
    // 同步状态
    await syncTabUpdated(updatedTab);
    
    return updatedTab;
  } catch (error) {
    console.error('Failed to update tab:', error);
    throw new TabOperationError('UPDATE_FAILED', error.message);
  }
}
```

#### refreshTab(tabId): Promise<void>
刷新标签页

```javascript
/**
 * 刷新标签页
 * @param {number} tabId - 标签页ID
 * @param {boolean} [bypassCache=false] - 是否绕过缓存
 * @returns {Promise<void>}
 */
async function refreshTab(tabId, bypassCache = false) {
  try {
    await chrome.tabs.reload(tabId, { bypassCache });
  } catch (error) {
    console.error('Failed to refresh tab:', error);
    throw new TabOperationError('REFRESH_FAILED', error.message);
  }
}
```

### 2.3 关闭标签页

#### closeTab(tabId): Promise<void>
关闭单个标签页

```javascript
/**
 * 关闭单个标签页
 * @param {number} tabId - 标签页ID
 * @returns {Promise<void>}
 */
async function closeTab(tabId) {
  try {
    await chrome.tabs.remove(tabId);
    
    // 同步状态
    await syncTabRemoved(tabId);
  } catch (error) {
    console.error('Failed to close tab:', error);
    throw new TabOperationError('CLOSE_FAILED', error.message);
  }
}
```

#### closeTabs(tabIds): Promise<void>
批量关闭标签页

```javascript
/**
 * 批量关闭标签页
 * @param {Array<number>} tabIds - 标签页ID数组
 * @param {Object} options - 批量操作选项
 * @returns {Promise<Object>} 操作结果
 */
async function closeTabs(tabIds, options = {}) {
  const { batchSize = 20 } = options;
  const errors = [];
  
  // 分批关闭
  for (let i = 0; i < tabIds.length; i += batchSize) {
    const batch = tabIds.slice(i, i + batchSize);
    
    try {
      await chrome.tabs.remove(batch);
      
      // 批量同步状态
      await Promise.all(batch.map(id => syncTabRemoved(id)));
    } catch (error) {
      errors.push({
        batch,
        error: error.message
      });
    }
  }
  
  return {
    success: tabIds.length - errors.length,
    errors
  };
}
```

## 3. 恢复会话功能

### 3.1 恢复窗口

```javascript
/**
 * 恢复单个窗口
 * @param {string} windowId - 窗口ID
 * @param {Object} options - 恢复选项
 * @returns {Promise<chrome.windows.Window>}
 */
async function restoreWindow(windowId, options = {}) {
  const {
    restoreToNewWindow = true,
    restorePosition = false,
    restorePinned = true
  } = options;
  
  // 获取窗口数据
  const windowData = await storage.getWindowData(windowId);
  if (!windowData) {
    throw new Error('Window data not found');
  }
  
  // 准备窗口创建参数
  const createParams = {
    focused: true,
    type: windowData.type || 'normal'
  };
  
  // 恢复位置
  if (restorePosition && windowData.position) {
    Object.assign(createParams, windowData.position);
  }
  
  // 恢复标签页
  const tabsToRestore = windowData.tabs.filter(tab => 
    restorePinned || !tab.pinned
  );
  
  if (restoreToNewWindow) {
    return await openInNewWindow(tabsToRestore, createParams);
  } else {
    // 恢复到当前窗口
    const results = await openTabs(tabsToRestore);
    return results;
  }
}
```

### 3.2 恢复会话

```javascript
/**
 * 恢复完整会话
 * @param {Object} sessionData - 会话数据
 * @param {Object} options - 恢复选项
 * @returns {Promise<void>}
 */
async function restoreSession(sessionData, options = {}) {
  const {
    onProgress,
    restoreOrder = true,
    skipInvalid = true
  } = options;
  
  const results = {
    windows: [],
    tabs: [],
    errors: []
  };
  
  try {
    // 按顺序恢复窗口
    if (restoreOrder && sessionData.windows) {
      for (const [index, windowData] of sessionData.windows.entries()) {
        try {
          const window = await restoreWindow(windowData.id, {
            restorePosition: true,
            restoreToNewWindow: true
          });
          
          results.windows.push(window);
          
          if (onProgress) {
            onProgress({
              type: 'window',
              current: index + 1,
              total: sessionData.windows.length
            });
          }
        } catch (error) {
          results.errors.push({
            type: 'window',
            data: windowData,
            error: error.message
          });
        }
      }
    }
    
    // 恢复独立标签页
    if (sessionData.tabs) {
      const tabResults = await openTabs(sessionData.tabs, {
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              type: 'tabs',
              ...progress
            });
          }
        }
      });
      
      results.tabs = tabResults.success;
      results.errors.push(...tabResults.errors);
    }
    
    return results;
  } catch (error) {
    console.error('Session restore failed:', error);
    throw error;
  }
}
```

## 4. 标签页状态同步

### 4.1 监听器设置

```javascript
/**
 * 初始化标签页状态监听器
 */
function initializeTabListeners() {
  // 标签页创建
  chrome.tabs.onCreated.addListener(async (tab) => {
    await onTabCreated(tab);
  });
  
  // 标签页更新
  chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    await onTabUpdated(tabId, changeInfo, tab);
  });
  
  // 标签页移除
  chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
    await onTabRemoved(tabId, removeInfo);
  });
  
  // 标签页移动
  chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
    await onTabMoved(tabId, moveInfo);
  });
  
  // 标签页激活
  chrome.tabs.onActivated.addListener(async (activeInfo) => {
    await onTabActivated(activeInfo);
  });
}
```

### 4.2 同步处理函数

```javascript
/**
 * 处理标签页创建事件
 */
async function onTabCreated(tab) {
  try {
    // 更新本地存储
    await storage.addTab({
      id: tab.id,
      windowId: tab.windowId,
      url: tab.url,
      title: tab.title,
      favIconUrl: tab.favIconUrl,
      index: tab.index,
      pinned: tab.pinned,
      active: tab.active,
      createdAt: Date.now()
    });
    
    // 发送状态更新
    await sendMessage({
      type: 'TAB_CREATED',
      payload: { tab }
    });
  } catch (error) {
    console.error('Tab sync failed:', error);
  }
}

/**
 * 处理标签页更新事件
 */
async function onTabUpdated(tabId, changeInfo, tab) {
  // 只处理有意义的更新
  const relevantChanges = ['url', 'title', 'favIconUrl', 'pinned', 'mutedInfo'];
  const hasRelevantChange = Object.keys(changeInfo).some(key => 
    relevantChanges.includes(key)
  );
  
  if (!hasRelevantChange) return;
  
  try {
    // 更新本地存储
    await storage.updateTab(tabId, {
      ...changeInfo,
      lastUpdated: Date.now()
    });
    
    // 发送状态更新
    await sendMessage({
      type: 'TAB_UPDATED',
      payload: { tabId, changeInfo, tab }
    });
  } catch (error) {
    console.error('Tab update sync failed:', error);
  }
}

/**
 * 处理标签页移除事件
 */
async function onTabRemoved(tabId, removeInfo) {
  try {
    // 保存到历史记录
    const tabData = await storage.getTab(tabId);
    if (tabData) {
      await storage.addToHistory({
        ...tabData,
        closedAt: Date.now(),
        windowClosing: removeInfo.isWindowClosing
      });
    }
    
    // 从活动存储中移除
    await storage.removeTab(tabId);
    
    // 发送状态更新
    await sendMessage({
      type: 'TAB_REMOVED',
      payload: { tabId, removeInfo }
    });
  } catch (error) {
    console.error('Tab removal sync failed:', error);
  }
}

/**
 * 处理标签页移动事件
 */
async function onTabMoved(tabId, moveInfo) {
  try {
    await storage.updateTab(tabId, {
      windowId: moveInfo.windowId,
      fromIndex: moveInfo.fromIndex,
      toIndex: moveInfo.toIndex
    });
    
    await sendMessage({
      type: 'TAB_MOVED',
      payload: { tabId, moveInfo }
    });
  } catch (error) {
    console.error('Tab move sync failed:', error);
  }
}
```

## 5. 高级操作功能

### 5.1 复制标签页

```javascript
/**
 * 复制标签页
 * @param {number} tabId - 源标签页ID
 * @param {Object} options - 复制选项
 * @returns {Promise<chrome.tabs.Tab>}
 */
async function duplicateTab(tabId, options = {}) {
  const {
    active = true,
    index,
    windowId
  } = options;
  
  try {
    const duplicatedTab = await chrome.tabs.duplicate(tabId, {
      active,
      index,
      windowId
    });
    
    return duplicatedTab;
  } catch (error) {
    // 如果浏览器不支持duplicate API，手动复制
    const sourceTab = await chrome.tabs.get(tabId);
    return await openTab({
      url: sourceTab.url,
      active,
      index: index ?? sourceTab.index + 1,
      windowId: windowId ?? sourceTab.windowId
    });
  }
}
```

### 5.2 固定/取消固定标签

```javascript
/**
 * 切换标签页固定状态
 * @param {number} tabId - 标签页ID
 * @param {boolean} [pinned] - 固定状态，不传则切换
 * @returns {Promise<boolean>} 新的固定状态
 */
async function togglePinTab(tabId, pinned) {
  const tab = await chrome.tabs.get(tabId);
  const newPinned = pinned ?? !tab.pinned;
  
  await chrome.tabs.update(tabId, { pinned: newPinned });
  return newPinned;
}
```

### 5.3 静音/取消静音

```javascript
/**
 * 切换标签页静音状态
 * @param {number} tabId - 标签页ID
 * @param {boolean} [muted] - 静音状态，不传则切换
 * @returns {Promise<boolean>} 新的静音状态
 */
async function toggleMuteTab(tabId, muted) {
  const tab = await chrome.tabs.get(tabId);
  const newMuted = muted ?? !tab.mutedInfo.muted;
  
  await chrome.tabs.update(tabId, { muted: newMuted });
  return newMuted;
}
```

### 5.4 标签页分组

```javascript
/**
 * 创建标签页组
 * @param {Array<number>} tabIds - 标签页ID数组
 * @param {Object} groupOptions - 分组选项
 * @returns {Promise<number>} 组ID
 */
async function createTabGroup(tabIds, groupOptions = {}) {
  // 检查是否支持标签组API
  if (!chrome.tabs.group) {
    throw new Error('Tab groups not supported');
  }
  
  const {
    title,
    color,
    collapsed = false
  } = groupOptions;
  
  try {
    // 创建组
    const groupId = await chrome.tabs.group({ tabIds });
    
    // 更新组属性
    if (title || color || collapsed) {
      await chrome.tabGroups.update(groupId, {
        title,
        color,
        collapsed
      });
    }
    
    return groupId;
  } catch (error) {
    console.error('Failed to create tab group:', error);
    throw error;
  }
}

/**
 * 解散标签页组
 * @param {number} groupId - 组ID
 * @returns {Promise<void>}
 */
async function ungroupTabs(groupId) {
  const tabs = await chrome.tabs.query({ groupId });
  const tabIds = tabs.map(tab => tab.id);
  
  if (tabIds.length > 0) {
    await chrome.tabs.ungroup(tabIds);
  }
}
```

## 6. 批量操作优化

### 6.1 限流处理

```javascript
/**
 * 创建限流的批量操作处理器
 */
class BatchProcessor {
  constructor(options = {}) {
    this.batchSize = options.batchSize || 10;
    this.delay = options.delay || 100;
    this.maxConcurrent = options.maxConcurrent || 3;
    this.queue = [];
    this.processing = false;
    this.abortController = null;
  }
  
  /**
   * 添加批量任务
   */
  async addBatch(items, processor) {
    const task = {
      items,
      processor,
      progress: 0,
      errors: [],
      results: []
    };
    
    this.queue.push(task);
    
    if (!this.processing) {
      this.process();
    }
    
    return task;
  }
  
  /**
   * 处理队列
   */
  async process() {
    this.processing = true;
    this.abortController = new AbortController();
    
    while (this.queue.length > 0 && !this.abortController.signal.aborted) {
      const activeTasks = this.queue.slice(0, this.maxConcurrent);
      
      await Promise.all(
        activeTasks.map(task => this.processTask(task))
      );
      
      // 移除完成的任务
      this.queue = this.queue.filter(task => 
        task.progress < task.items.length
      );
      
      if (this.queue.length > 0) {
        await sleep(this.delay);
      }
    }
    
    this.processing = false;
  }
  
  /**
   * 处理单个任务
   */
  async processTask(task) {
    const { items, processor } = task;
    
    for (let i = task.progress; i < items.length; i += this.batchSize) {
      if (this.abortController.signal.aborted) break;
      
      const batch = items.slice(i, Math.min(i + this.batchSize, items.length));
      
      const results = await Promise.allSettled(
        batch.map(item => processor(item))
      );
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          task.results.push(result.value);
        } else {
          task.errors.push({
            index: i + index,
            error: result.reason,
            item: batch[index]
          });
        }
      });
      
      task.progress = i + batch.length;
      
      // 触发进度更新
      if (task.onProgress) {
        task.onProgress({
          completed: task.results.length,
          failed: task.errors.length,
          total: items.length,
          percentage: (task.progress / items.length) * 100
        });
      }
      
      if (i + this.batchSize < items.length) {
        await sleep(this.delay);
      }
    }
  }
  
  /**
   * 取消所有操作
   */
  cancel() {
    if (this.abortController) {
      this.abortController.abort();
    }
  }
}
```

### 6.2 进度反馈机制

```javascript
/**
 * 进度管理器
 */
class ProgressManager {
  constructor() {
    this.tasks = new Map();
  }
  
  /**
   * 创建进度任务
   */
  createTask(taskId, total, description) {
    const task = {
      id: taskId,
      total,
      completed: 0,
      failed: 0,
      description,
      startTime: Date.now(),
      status: 'running',
      details: []
    };
    
    this.tasks.set(taskId, task);
    this.notifyProgress(taskId);
    
    return task;
  }
  
  /**
   * 更新进度
   */
  updateProgress(taskId, update) {
    const task = this.tasks.get(taskId);
    if (!task) return;
    
    Object.assign(task, update);
    
    // 计算进度百分比
    task.percentage = (task.completed / task.total) * 100;
    
    // 估算剩余时间
    const elapsed = Date.now() - task.startTime;
    const rate = task.completed / elapsed;
    task.estimatedRemaining = rate > 0 
      ? (task.total - task.completed) / rate 
      : 0;
    
    this.notifyProgress(taskId);
  }
  
  /**
   * 完成任务
   */
  completeTask(taskId, status = 'completed') {
    const task = this.tasks.get(taskId);
    if (!task) return;
    
    task.status = status;
    task.endTime = Date.now();
    task.duration = task.endTime - task.startTime;
    
    this.notifyProgress(taskId);
    
    // 延迟清理
    setTimeout(() => {
      this.tasks.delete(taskId);
    }, 5000);
  }
  
  /**
   * 通知进度更新
   */
  notifyProgress(taskId) {
    const task = this.tasks.get(taskId);
    if (!task) return;
    
    // 发送进度消息到UI
    sendMessage({
      type: 'PROGRESS_UPDATE',
      payload: { task }
    });
  }
}
```

## 7. 错误处理

### 7.1 错误类型定义

```javascript
/**
 * 标签页操作错误类
 */
class TabOperationError extends Error {
  constructor(code, message, details = {}) {
    super(message);
    this.name = 'TabOperationError';
    this.code = code;
    this.details = details;
  }
}

// 错误代码常量
const ERROR_CODES = {
  INVALID_URL: 'INVALID_URL',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  TAB_NOT_FOUND: 'TAB_NOT_FOUND',
  WINDOW_NOT_FOUND: 'WINDOW_NOT_FOUND',
  OPERATION_FAILED: 'OPERATION_FAILED',
  BATCH_PARTIAL_FAILURE: 'BATCH_PARTIAL_FAILURE',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  NETWORK_ERROR: 'NETWORK_ERROR'
};
```

### 7.2 URL验证

```javascript
/**
 * 验证和规范化URL
 * @param {string} url - 待验证的URL
 * @returns {string} 规范化的URL
 */
function validateUrl(url) {
  if (!url) {
    throw new TabOperationError(
      ERROR_CODES.INVALID_URL,
      'URL is required'
    );
  }
  
  // 处理特殊协议
  const specialProtocols = ['chrome://', 'chrome-extension://', 'about:'];
  if (specialProtocols.some(protocol => url.startsWith(protocol))) {
    return url;
  }
  
  // 添加协议
  if (!/^https?:\/\//i.test(url)) {
    url = 'https://' + url;
  }
  
  try {
    const urlObj = new URL(url);
    
    // 检查是否是有效的HTTP(S) URL
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      throw new Error('Invalid protocol');
    }
    
    return urlObj.href;
  } catch (error) {
    throw new TabOperationError(
      ERROR_CODES.INVALID_URL,
      'Invalid URL format',
      { originalUrl: url, error: error.message }
    );
  }
}
```

### 7.3 权限处理

```javascript
/**
 * 检查和请求必要的权限
 * @param {string} operation - 操作类型
 * @returns {Promise<boolean>}
 */
async function checkPermissions(operation) {
  const requiredPermissions = {
    tabs: ['tabs'],
    windows: ['tabs', 'windows'],
    sessions: ['tabs', 'sessions']
  };
  
  const needed = requiredPermissions[operation] || ['tabs'];
  
  try {
    const hasPermission = await chrome.permissions.contains({
      permissions: needed
    });
    
    if (!hasPermission) {
      // 请求权限
      const granted = await chrome.permissions.request({
        permissions: needed
      });
      
      if (!granted) {
        throw new TabOperationError(
          ERROR_CODES.PERMISSION_DENIED,
          'Required permissions not granted',
          { needed }
        );
      }
    }
    
    return true;
  } catch (error) {
    console.error('Permission check failed:', error);
    throw error;
  }
}
```

### 7.4 重试机制

```javascript
/**
 * 带重试的操作执行器
 * @param {Function} operation - 要执行的操作
 * @param {Object} options - 重试选项
 * @returns {Promise<any>}
 */
async function withRetry(operation, options = {}) {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    backoffFactor = 2,
    shouldRetry = (error) => true
  } = options;
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // 检查是否应该重试
      if (attempt === maxRetries || !shouldRetry(error)) {
        throw error;
      }
      
      // 计算延迟时间
      const delay = initialDelay * Math.pow(backoffFactor, attempt);
      console.warn(`Operation failed, retrying in ${delay}ms...`, error);
      
      await sleep(delay);
    }
  }
  
  throw lastError;
}

/**
 * 判断错误是否可重试
 */
function isRetryableError(error) {
  const retryableCodes = [
    'NETWORK_ERROR',
    'TIMEOUT',
    'TEMPORARY_FAILURE'
  ];
  
  return retryableCodes.includes(error.code) ||
         error.message.includes('temporarily unavailable');
}
```

## 8. Chrome API交互最佳实践

### 8.1 API调用优化

```javascript
/**
 * 批量获取标签页信息
 * 使用单次查询代替多次get调用
 */
async function getTabsInfo(tabIds) {
  if (!tabIds || tabIds.length === 0) return [];
  
  // 使用query一次性获取所有标签页
  const allTabs = await chrome.tabs.query({});
  const tabMap = new Map(allTabs.map(tab => [tab.id, tab]));
  
  return tabIds.map(id => tabMap.get(id)).filter(Boolean);
}

/**
 * 智能窗口焦点管理
 * 避免频繁切换焦点影响用户体验
 */
async function smartFocusWindow(windowId) {
  const currentWindow = await chrome.windows.getCurrent();
  
  // 如果目标窗口已经是当前窗口，不需要切换
  if (currentWindow.id === windowId) return;
  
  // 检查用户设置
  const settings = await storage.getSettings();
  if (!settings.autoFocusWindows) return;
  
  await chrome.windows.update(windowId, { focused: true });
}
```

### 8.2 性能优化

```javascript
/**
 * 缓存管理器
 * 减少重复的API调用
 */
class TabCache {
  constructor(ttl = 5000) {
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  get(tabId) {
    const cached = this.cache.get(tabId);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(tabId);
      return null;
    }
    
    return cached.data;
  }
  
  set(tabId, data) {
    this.cache.set(tabId, {
      data,
      timestamp: Date.now()
    });
  }
  
  clear() {
    this.cache.clear();
  }
}

// 使用缓存的标签页获取
const tabCache = new TabCache();

async function getCachedTab(tabId) {
  let tab = tabCache.get(tabId);
  
  if (!tab) {
    tab = await chrome.tabs.get(tabId);
    tabCache.set(tabId, tab);
  }
  
  return tab;
}
```

### 8.3 事件去抖动

```javascript
/**
 * 事件去抖动处理器
 * 避免频繁的状态更新
 */
class DebouncedEventHandler {
  constructor(handler, delay = 300) {
    this.handler = handler;
    this.delay = delay;
    this.timers = new Map();
  }
  
  handle(key, ...args) {
    // 清除之前的定时器
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }
    
    // 设置新的定时器
    const timer = setTimeout(() => {
      this.handler(...args);
      this.timers.delete(key);
    }, this.delay);
    
    this.timers.set(key, timer);
  }
  
  flush(key) {
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }
  }
}

// 使用去抖动的更新处理
const debouncedUpdate = new DebouncedEventHandler(async (tabId, changes) => {
  await storage.updateTab(tabId, changes);
});

// 在标签页更新事件中使用
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  debouncedUpdate.handle(tabId, tabId, changeInfo);
});
```

## 9. 模块导出

```javascript
// tabOperations.js
export {
  // 核心操作
  openTab,
  openTabs,
  openInNewWindow,
  updateTabInfo,
  refreshTab,
  closeTab,
  closeTabs,
  
  // 高级操作
  duplicateTab,
  togglePinTab,
  toggleMuteTab,
  createTabGroup,
  ungroupTabs,
  
  // 会话恢复
  restoreWindow,
  restoreSession,
  
  // 工具类
  BatchProcessor,
  ProgressManager,
  TabCache,
  
  // 错误类型
  TabOperationError,
  ERROR_CODES,
  
  // 辅助函数
  validateUrl,
  checkPermissions,
  withRetry
};
```

## 10. 使用示例

```javascript
// 批量打开标签页并显示进度
async function openMultipleTabs() {
  const urls = [
    'https://example.com',
    'https://google.com',
    'https://github.com'
  ];
  
  const progressManager = new ProgressManager();
  const taskId = 'open-tabs-' + Date.now();
  
  progressManager.createTask(taskId, urls.length, 'Opening tabs...');
  
  try {
    const results = await openTabs(
      urls.map(url => ({ url })),
      {
        batchSize: 5,
        onProgress: (progress) => {
          progressManager.updateProgress(taskId, progress);
        }
      }
    );
    
    progressManager.completeTask(taskId, 'completed');
    console.log('Opened tabs:', results.success);
    
    if (results.errors.length > 0) {
      console.error('Failed tabs:', results.errors);
    }
  } catch (error) {
    progressManager.completeTask(taskId, 'failed');
    throw error;
  }
}

// 恢复会话示例
async function restoreLastSession() {
  const lastSession = await storage.getLastSession();
  
  if (!lastSession) {
    console.log('No session to restore');
    return;
  }
  
  const results = await restoreSession(lastSession, {
    restoreOrder: true,
    onProgress: (progress) => {
      console.log(`Restoring ${progress.type}: ${progress.current}/${progress.total}`);
    }
  });
  
  console.log('Restore complete:', results);
}
```