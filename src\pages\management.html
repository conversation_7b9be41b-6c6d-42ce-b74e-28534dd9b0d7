<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MyTab3 - 标签页管理</title>
  <link rel="stylesheet" href="management.css">
</head>
<body>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <header class="toolbar">
      <div class="toolbar-left">
        <button class="btn-import-browser">导入浏览器数据</button>
        <button class="btn-import">导入会话</button>
        <button class="btn-export">导出会话</button>
      </div>
      <div class="toolbar-center">
        <input type="search" class="search-box" placeholder="搜索标签页...">
      </div>
      <div class="toolbar-right">
        <div class="view-switcher">
          <button class="view-btn active" data-view="card">卡片</button>
          <button class="view-btn" data-view="list">列表</button>
          <button class="view-btn" data-view="compact">紧凑</button>
        </div>
        <button class="btn-settings">设置</button>
      </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 左侧窗口列表 -->
      <aside class="window-sidebar">
        <div class="window-list-container">
          <!-- 窗口组列表将通过JS动态渲染 -->
        </div>
      </aside>

      <!-- 右侧标签页内容区 -->
      <section class="tabs-content">
        <div class="tabs-container">
          <!-- 标签页卡片列表将通过JS动态渲染 -->
        </div>
      </section>
    </main>

    <!-- 底部状态栏 -->
    <footer class="status-bar">
      <div class="status-left">
        <span class="tab-count">共 0 个标签页</span>
        <span class="window-count">0 个窗口</span>
      </div>
      <div class="status-right">
        <span class="last-sync">最后同步: --:--</span>
      </div>
    </footer>
  </div>

  <!-- 设置面板（初始隐藏） -->
  <div class="settings-overlay"></div>
  <div class="settings-panel"></div>

  <!-- 上下文菜单（动态创建） -->
  
  <!-- 脚本加载 -->
  <script src="../components/window-list.js"></script>
  <script src="../components/tab-card.js"></script>
  <script src="../components/toolbar.js"></script>
  <script src="../components/settings-panel.js"></script>
  <script src="../utils/virtual-scroll.js"></script>
  <script src="../utils/drag-drop.js"></script>
  <script src="management.js"></script>
</body>
</html>