/**
 * Toolbar 组件
 * 顶部工具栏，包含搜索、导入导出、视图切换等功能
 */
class Toolbar {
  constructor(container) {
    this.container = container;
    this.searchInput = container.querySelector('.search-box');
    this.viewSwitcher = container.querySelector('.view-switcher');
    this.importBrowserBtn = container.querySelector('.btn-import-browser');
    this.importBtn = container.querySelector('.btn-import');
    this.exportBtn = container.querySelector('.btn-export');
    this.settingsBtn = container.querySelector('.btn-settings');

    this.currentView = 'card';
    this.searchQuery = '';

    // 事件回调
    this.onSearch = null;
    this.onViewChange = null;
    this.onImportBrowser = null;
    this.onImport = null;
    this.onExport = null;
    this.onSettings = null;

    this.init();
  }

  /**
   * 初始化组件
   */
  init() {
    this.attachEventListeners();
    this.loadSavedView();
  }

  /**
   * 绑定事件监听器
   */
  attachEventListeners() {
    // 搜索功能
    if (this.searchInput) {
      this.searchInput.addEventListener('input', this.debounce((e) => {
        this.searchQuery = e.target.value;
        if (this.onSearch) {
          this.onSearch(this.searchQuery);
        }
        this.dispatchEvent('search', { query: this.searchQuery });
      }, 300));

      // 清空搜索框
      this.searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.clearSearch();
        }
      });
    }

    // 视图切换
    if (this.viewSwitcher) {
      this.viewSwitcher.addEventListener('click', (e) => {
        const viewBtn = e.target.closest('.view-btn');
        if (viewBtn) {
          const view = viewBtn.dataset.view;
          this.switchView(view);
        }
      });
    }

    // 导入浏览器数据按钮
    if (this.importBrowserBtn) {
      this.importBrowserBtn.addEventListener('click', () => {
        this.handleImportBrowser();
      });
    }

    // 导入按钮
    if (this.importBtn) {
      this.importBtn.addEventListener('click', () => {
        this.handleImport();
      });
    }

    // 导出按钮
    if (this.exportBtn) {
      this.exportBtn.addEventListener('click', () => {
        this.handleExport();
      });
    }

    // 设置按钮
    if (this.settingsBtn) {
      this.settingsBtn.addEventListener('click', () => {
        if (this.onSettings) {
          this.onSettings();
        }
        this.dispatchEvent('settings');
      });
    }

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcut(e);
    });
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboardShortcut(e) {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
      e.preventDefault();
      this.focusSearch();
    }
    
    // Ctrl/Cmd + E: 导出
    if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
      e.preventDefault();
      this.handleExport();
    }
    
    // Ctrl/Cmd + I: 导入
    if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
      e.preventDefault();
      this.handleImport();
    }
    
    // Alt + 1/2/3: 切换视图
    if (e.altKey) {
      switch(e.key) {
        case '1':
          e.preventDefault();
          this.switchView('card');
          break;
        case '2':
          e.preventDefault();
          this.switchView('list');
          break;
        case '3':
          e.preventDefault();
          this.switchView('compact');
          break;
      }
    }
  }

  /**
   * 切换视图
   */
  switchView(viewMode) {
    if (this.currentView === viewMode) return;
    
    this.currentView = viewMode;
    
    // 更新按钮状态
    if (this.viewSwitcher) {
      this.viewSwitcher.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === viewMode);
      });
    }
    
    // 保存视图设置
    this.saveViewPreference(viewMode);
    
    // 触发视图变更事件
    if (this.onViewChange) {
      this.onViewChange(viewMode);
    }
    this.dispatchEvent('viewChange', { viewMode });
  }

  /**
   * 处理从浏览器导入数据
   */
  async handleImportBrowser() {
    try {
      this.showToast('正在导入浏览器数据...', 'info');

      // 通过消息传递获取浏览器数据
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BROWSER_DATA',
        payload: { includeIncognito: false }
      });

      // 处理MessageHandler的响应格式
      const data = response && response.data ? response.data : response;

      if (data && data.windows) {
        // 转换数据格式
        const importData = {
          version: '1.0.0',
          timestamp: new Date().toISOString(),
          windows: data.windows.map(window => ({
            id: window.id,
            name: `浏览器窗口 ${window.id}`,
            state: window.state,
            tabs: window.tabs || []
          }))
        };

        if (this.onImportBrowser) {
          await this.onImportBrowser(importData);
        }

        this.dispatchEvent('importBrowser', { data: importData });
        this.showToast('浏览器数据导入成功');
      } else {
        throw new Error('无法获取浏览器数据');
      }
    } catch (error) {
      console.error('导入浏览器数据失败:', error);
      this.showToast('导入浏览器数据失败：' + error.message, 'error');
    }
  }

  /**
   * 处理导入
   */
  handleImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          if (this.onImport) {
            this.onImport(data);
          }
          this.dispatchEvent('import', { data });
          this.showToast('会话导入成功');
        } catch (error) {
          console.error('导入失败:', error);
          this.showToast('导入失败：文件格式错误', 'error');
        }
      };
      reader.readAsText(file);
    };

    input.click();
  }

  /**
   * 处理导出
   */
  handleExport() {
    if (this.onExport) {
      const data = this.onExport();
      this.downloadJSON(data, `mytab3_session_${this.getTimestamp()}.json`);
    } else {
      this.dispatchEvent('export');
    }
  }

  /**
   * 下载JSON文件
   */
  downloadJSON(data, filename) {
    const json = JSON.stringify(data, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    this.showToast('会话导出成功');
  }

  /**
   * 聚焦搜索框
   */
  focusSearch() {
    if (this.searchInput) {
      this.searchInput.focus();
      this.searchInput.select();
    }
  }

  /**
   * 清空搜索
   */
  clearSearch() {
    if (this.searchInput) {
      this.searchInput.value = '';
      this.searchQuery = '';
      if (this.onSearch) {
        this.onSearch('');
      }
      this.dispatchEvent('search', { query: '' });
    }
  }

  /**
   * 设置搜索框值
   */
  setSearchValue(value) {
    if (this.searchInput) {
      this.searchInput.value = value;
      this.searchQuery = value;
    }
  }

  /**
   * 加载保存的视图设置
   */
  loadSavedView() {
    const savedView = localStorage.getItem('mytab3_view_mode');
    if (savedView && ['card', 'list', 'compact'].includes(savedView)) {
      this.switchView(savedView);
    }
  }

  /**
   * 保存视图偏好
   */
  saveViewPreference(viewMode) {
    localStorage.setItem('mytab3_view_mode', viewMode);
  }

  /**
   * 显示提示信息
   */
  showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${type === 'error' ? '#d93025' : '#1e8e3e'};
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      font-size: 14px;
      z-index: 10000;
      animation: fadeIn 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.animation = 'fadeOut 0.3s ease';
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  /**
   * 获取时间戳
   */
  getTimestamp() {
    const now = new Date();
    return now.getFullYear() +
           String(now.getMonth() + 1).padStart(2, '0') +
           String(now.getDate()).padStart(2, '0') +
           '_' +
           String(now.getHours()).padStart(2, '0') +
           String(now.getMinutes()).padStart(2, '0') +
           String(now.getSeconds()).padStart(2, '0');
  }

  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 触发自定义事件
   */
  dispatchEvent(eventName, detail = {}) {
    document.dispatchEvent(new CustomEvent(eventName, { detail }));
  }

  /**
   * 更新工具栏状态
   */
  updateStatus(tabCount, windowCount) {
    // 这个方法可以用于更新工具栏上的状态信息
    // 比如显示当前标签页数量等
  }

  /**
   * 启用/禁用按钮
   */
  setButtonEnabled(buttonName, enabled) {
    const button = this[`${buttonName}Btn`];
    if (button) {
      button.disabled = !enabled;
    }
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 清理事件监听器
    this.searchInput = null;
    this.viewSwitcher = null;
    this.importBtn = null;
    this.exportBtn = null;
    this.settingsBtn = null;
  }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Toolbar;
}