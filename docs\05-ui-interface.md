# UI界面模块详细需求文档

## 1. 模块概述

MyTab3 UI界面模块采用完整网页式管理界面设计，而非传统的浏览器扩展弹窗。通过 `chrome.tabs.create()` API 在新标签页中打开管理页面，提供更宽敞的操作空间和更好的用户体验。

### 1.1 核心特性
- 全屏管理界面，充分利用浏览器窗口空间
- 响应式设计，适配不同屏幕尺寸
- 模块化组件架构，易于维护和扩展
- 高性能渲染，支持大量标签页管理

### 1.2 技术选型
- 原生 HTML/CSS/JavaScript 开发
- 避免引入大型前端框架，保持轻量级
- 使用 Web Components 思想进行组件化开发

## 2. 页面结构设计

### 2.1 主布局架构

```html
<div class="app-container">
  <!-- 顶部工具栏 -->
  <header class="toolbar">
    <div class="toolbar-left">
      <button class="btn-import">导入会话</button>
      <button class="btn-export">导出会话</button>
    </div>
    <div class="toolbar-center">
      <input type="search" class="search-box" placeholder="搜索标签页...">
    </div>
    <div class="toolbar-right">
      <div class="view-switcher">
        <button class="view-btn active" data-view="card">卡片</button>
        <button class="view-btn" data-view="list">列表</button>
        <button class="view-btn" data-view="compact">紧凑</button>
      </div>
      <button class="btn-settings">设置</button>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="main-content">
    <!-- 左侧窗口列表 -->
    <aside class="window-sidebar">
      <div class="window-list-container">
        <!-- 窗口组列表 -->
      </div>
    </aside>

    <!-- 右侧标签页内容区 -->
    <section class="tabs-content">
      <div class="tabs-container">
        <!-- 标签页卡片列表 -->
      </div>
    </section>
  </main>

  <!-- 底部状态栏 -->
  <footer class="status-bar">
    <div class="status-left">
      <span class="tab-count">共 0 个标签页</span>
      <span class="window-count">0 个窗口</span>
    </div>
    <div class="status-right">
      <span class="last-sync">最后同步: --:--</span>
    </div>
  </footer>
</div>
```

### 2.2 响应式设计要求

```css
/* 桌面端 (>= 1200px) */
@media (min-width: 1200px) {
  .window-sidebar { width: 300px; }
  .tabs-content { margin-left: 300px; }
  .tab-card { width: calc(25% - 20px); }
}

/* 平板端 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .window-sidebar { width: 250px; }
  .tabs-content { margin-left: 250px; }
  .tab-card { width: calc(33.333% - 20px); }
}

/* 移动端 (< 768px) */
@media (max-width: 767px) {
  .window-sidebar { 
    position: fixed;
    left: -100%;
    width: 80%;
    z-index: 1000;
  }
  .window-sidebar.active { left: 0; }
  .tabs-content { margin-left: 0; }
  .tab-card { width: 100%; }
}
```

## 3. 核心UI组件

### 3.1 WindowList 组件

窗口列表组件，显示所有窗口组信息。

```javascript
class WindowList {
  constructor(container) {
    this.container = container;
    this.windows = [];
    this.selectedWindowId = null;
  }

  render() {
    const html = this.windows.map(window => `
      <div class="window-item ${window.id === this.selectedWindowId ? 'selected' : ''}" 
           data-window-id="${window.id}"
           draggable="true">
        <div class="window-header">
          <span class="window-toggle ${window.collapsed ? 'collapsed' : ''}">▼</span>
          <span class="window-icon">🪟</span>
          <span class="window-title">${this.getWindowTitle(window)}</span>
          <span class="window-tab-count">${window.tabs.length}</span>
        </div>
        <div class="window-tabs ${window.collapsed ? 'hidden' : ''}">
          ${this.renderWindowTabs(window.tabs)}
        </div>
      </div>
    `).join('');
    
    this.container.innerHTML = html;
    this.attachEventListeners();
  }

  getWindowTitle(window) {
    return window.customName || `窗口 ${window.id}`;
  }

  renderWindowTabs(tabs) {
    return tabs.slice(0, 5).map(tab => `
      <div class="window-tab-preview">
        <img src="${tab.favIconUrl || 'default-icon.png'}" alt="">
        <span>${tab.title}</span>
      </div>
    `).join('');
  }

  attachEventListeners() {
    // 折叠/展开
    this.container.querySelectorAll('.window-toggle').forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleWindow(e.target.closest('.window-item'));
      });
    });

    // 拖拽排序
    this.initDragAndDrop();
  }

  initDragAndDrop() {
    let draggedElement = null;

    this.container.querySelectorAll('.window-item').forEach(item => {
      item.addEventListener('dragstart', (e) => {
        draggedElement = e.target;
        e.target.classList.add('dragging');
      });

      item.addEventListener('dragend', (e) => {
        e.target.classList.remove('dragging');
      });

      item.addEventListener('dragover', (e) => {
        e.preventDefault();
        const afterElement = this.getDragAfterElement(e.clientY);
        if (afterElement == null) {
          this.container.appendChild(draggedElement);
        } else {
          this.container.insertBefore(draggedElement, afterElement);
        }
      });
    });
  }

  getDragAfterElement(y) {
    const draggableElements = [...this.container.querySelectorAll('.window-item:not(.dragging)')];
    
    return draggableElements.reduce((closest, child) => {
      const box = child.getBoundingClientRect();
      const offset = y - box.top - box.height / 2;
      
      if (offset < 0 && offset > closest.offset) {
        return { offset: offset, element: child };
      } else {
        return closest;
      }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
  }
}
```

### 3.2 TabCard 组件

标签页卡片组件，显示单个标签页信息。

```javascript
class TabCard {
  constructor(tab, viewMode = 'card') {
    this.tab = tab;
    this.viewMode = viewMode;
  }

  render() {
    switch(this.viewMode) {
      case 'card':
        return this.renderCardView();
      case 'list':
        return this.renderListView();
      case 'compact':
        return this.renderCompactView();
    }
  }

  renderCardView() {
    return `
      <div class="tab-card" data-tab-id="${this.tab.id}">
        <div class="tab-card-header">
          <input type="checkbox" class="tab-select" data-tab-id="${this.tab.id}">
          <img class="tab-favicon" src="${this.tab.favIconUrl || 'default-icon.png'}" alt="">
          <span class="tab-domain">${this.getDomain(this.tab.url)}</span>
        </div>
        <div class="tab-card-body">
          <h3 class="tab-title">${this.tab.title}</h3>
          <p class="tab-url">${this.tab.url}</p>
        </div>
        <div class="tab-card-footer">
          <button class="tab-action open" title="打开">📂</button>
          <button class="tab-action copy" title="复制链接">📋</button>
          <button class="tab-action close" title="关闭">❌</button>
        </div>
      </div>
    `;
  }

  renderListView() {
    return `
      <div class="tab-list-item" data-tab-id="${this.tab.id}">
        <input type="checkbox" class="tab-select" data-tab-id="${this.tab.id}">
        <img class="tab-favicon" src="${this.tab.favIconUrl || 'default-icon.png'}" alt="">
        <div class="tab-info">
          <span class="tab-title">${this.tab.title}</span>
          <span class="tab-url">${this.tab.url}</span>
        </div>
        <div class="tab-actions">
          <button class="tab-action open">📂</button>
          <button class="tab-action copy">📋</button>
          <button class="tab-action close">❌</button>
        </div>
      </div>
    `;
  }

  renderCompactView() {
    return `
      <div class="tab-compact-item" data-tab-id="${this.tab.id}">
        <input type="checkbox" class="tab-select" data-tab-id="${this.tab.id}">
        <img class="tab-favicon-small" src="${this.tab.favIconUrl || 'default-icon.png'}" alt="">
        <span class="tab-title-compact">${this.tab.title}</span>
      </div>
    `;
  }

  getDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }
}
```

### 3.3 Toolbar 组件

顶部工具栏组件。

```javascript
class Toolbar {
  constructor(container) {
    this.container = container;
    this.searchInput = container.querySelector('.search-box');
    this.viewSwitcher = container.querySelector('.view-switcher');
  }

  init() {
    // 搜索功能
    this.searchInput.addEventListener('input', (e) => {
      this.onSearch(e.target.value);
    });

    // 视图切换
    this.viewSwitcher.addEventListener('click', (e) => {
      if (e.target.classList.contains('view-btn')) {
        this.switchView(e.target.dataset.view);
      }
    });

    // 导入导出按钮
    this.container.querySelector('.btn-import').addEventListener('click', () => {
      this.onImport();
    });

    this.container.querySelector('.btn-export').addEventListener('click', () => {
      this.onExport();
    });
  }

  onSearch(query) {
    // 触发搜索事件
    document.dispatchEvent(new CustomEvent('search', { detail: { query } }));
  }

  switchView(viewMode) {
    // 更新激活状态
    this.viewSwitcher.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.view === viewMode);
    });

    // 触发视图切换事件
    document.dispatchEvent(new CustomEvent('viewChange', { detail: { viewMode } }));
  }

  onImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          document.dispatchEvent(new CustomEvent('import', { 
            detail: { data: JSON.parse(e.target.result) } 
          }));
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  onExport() {
    document.dispatchEvent(new CustomEvent('export'));
  }
}
```

### 3.4 SettingsPanel 组件

设置面板组件。

```javascript
class SettingsPanel {
  constructor() {
    this.visible = false;
    this.settings = this.loadSettings();
  }

  render() {
    return `
      <div class="settings-panel ${this.visible ? 'visible' : ''}">
        <div class="settings-header">
          <h2>设置</h2>
          <button class="settings-close">✕</button>
        </div>
        <div class="settings-body">
          <div class="setting-group">
            <h3>常规设置</h3>
            <label class="setting-item">
              <span>自动保存会话</span>
              <input type="checkbox" name="autoSave" ${this.settings.autoSave ? 'checked' : ''}>
            </label>
            <label class="setting-item">
              <span>保存间隔（分钟）</span>
              <input type="number" name="saveInterval" value="${this.settings.saveInterval}" min="1" max="60">
            </label>
          </div>
          
          <div class="setting-group">
            <h3>显示设置</h3>
            <label class="setting-item">
              <span>默认视图</span>
              <select name="defaultView">
                <option value="card" ${this.settings.defaultView === 'card' ? 'selected' : ''}>卡片视图</option>
                <option value="list" ${this.settings.defaultView === 'list' ? 'selected' : ''}>列表视图</option>
                <option value="compact" ${this.settings.defaultView === 'compact' ? 'selected' : ''}>紧凑视图</option>
              </select>
            </label>
            <label class="setting-item">
              <span>显示网站图标</span>
              <input type="checkbox" name="showFavicons" ${this.settings.showFavicons ? 'checked' : ''}>
            </label>
          </div>
          
          <div class="setting-group">
            <h3>高级设置</h3>
            <label class="setting-item">
              <span>启用虚拟滚动</span>
              <input type="checkbox" name="virtualScroll" ${this.settings.virtualScroll ? 'checked' : ''}>
            </label>
            <label class="setting-item">
              <span>每页显示标签数</span>
              <input type="number" name="tabsPerPage" value="${this.settings.tabsPerPage}" min="10" max="200">
            </label>
          </div>
        </div>
        <div class="settings-footer">
          <button class="btn-save">保存设置</button>
          <button class="btn-reset">恢复默认</button>
        </div>
      </div>
    `;
  }

  loadSettings() {
    const defaults = {
      autoSave: true,
      saveInterval: 5,
      defaultView: 'card',
      showFavicons: true,
      virtualScroll: true,
      tabsPerPage: 50
    };
    
    const saved = localStorage.getItem('mytab3_settings');
    return saved ? { ...defaults, ...JSON.parse(saved) } : defaults;
  }

  saveSettings() {
    localStorage.setItem('mytab3_settings', JSON.stringify(this.settings));
    document.dispatchEvent(new CustomEvent('settingsChanged', { detail: this.settings }));
  }
}
```

## 4. 交互设计

### 4.1 点击事件

```javascript
class InteractionManager {
  constructor() {
    this.selectedTabs = new Set();
    this.lastClickedTab = null;
  }

  init() {
    // 单击选择
    document.addEventListener('click', (e) => {
      const tabCard = e.target.closest('.tab-card, .tab-list-item, .tab-compact-item');
      if (tabCard) {
        this.handleTabClick(tabCard, e);
      }
    });

    // 双击打开标签
    document.addEventListener('dblclick', (e) => {
      const tabCard = e.target.closest('.tab-card, .tab-list-item, .tab-compact-item');
      if (tabCard) {
        this.openTab(tabCard.dataset.tabId);
      }
    });

    // 右键菜单
    document.addEventListener('contextmenu', (e) => {
      const tabCard = e.target.closest('.tab-card, .tab-list-item, .tab-compact-item');
      if (tabCard) {
        e.preventDefault();
        this.showContextMenu(e.pageX, e.pageY, tabCard.dataset.tabId);
      }
    });
  }

  handleTabClick(tabCard, event) {
    const tabId = tabCard.dataset.tabId;
    const checkbox = tabCard.querySelector('.tab-select');

    if (event.shiftKey && this.lastClickedTab) {
      // Shift+Click: 范围选择
      this.selectRange(this.lastClickedTab, tabId);
    } else if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd+Click: 添加/移除选择
      this.toggleSelection(tabId);
      checkbox.checked = this.selectedTabs.has(tabId);
    } else {
      // 普通点击: 单选
      this.clearSelection();
      this.selectedTabs.add(tabId);
      checkbox.checked = true;
    }

    this.lastClickedTab = tabId;
    this.updateSelectionUI();
  }

  showContextMenu(x, y, tabId) {
    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.innerHTML = `
      <div class="context-menu-item" data-action="open">在新窗口打开</div>
      <div class="context-menu-item" data-action="open-background">在后台打开</div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item" data-action="copy-url">复制链接</div>
      <div class="context-menu-item" data-action="copy-title">复制标题</div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item" data-action="close">关闭标签</div>
      <div class="context-menu-item" data-action="close-others">关闭其他标签</div>
    `;

    menu.style.left = `${x}px`;
    menu.style.top = `${y}px`;
    document.body.appendChild(menu);

    // 自动关闭
    const closeMenu = () => {
      menu.remove();
      document.removeEventListener('click', closeMenu);
    };
    setTimeout(() => document.addEventListener('click', closeMenu), 0);

    // 处理菜单项点击
    menu.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      if (action) {
        this.handleContextMenuAction(action, tabId);
      }
    });
  }

  openTab(tabId) {
    chrome.tabs.update(parseInt(tabId), { active: true });
  }
}
```

### 4.2 拖拽功能

```javascript
class DragDropManager {
  constructor() {
    this.draggedTab = null;
    this.draggedData = null;
  }

  init() {
    // 标签拖拽
    document.addEventListener('dragstart', (e) => {
      const tabElement = e.target.closest('.tab-card, .tab-list-item');
      if (tabElement) {
        this.handleTabDragStart(e, tabElement);
      }
    });

    // 拖拽到窗口
    document.addEventListener('dragover', (e) => {
      const windowItem = e.target.closest('.window-item');
      if (windowItem && this.draggedTab) {
        e.preventDefault();
        windowItem.classList.add('drag-over');
      }
    });

    document.addEventListener('dragleave', (e) => {
      const windowItem = e.target.closest('.window-item');
      if (windowItem) {
        windowItem.classList.remove('drag-over');
      }
    });

    document.addEventListener('drop', (e) => {
      const windowItem = e.target.closest('.window-item');
      if (windowItem && this.draggedTab) {
        e.preventDefault();
        this.handleTabDrop(windowItem.dataset.windowId);
        windowItem.classList.remove('drag-over');
      }
    });
  }

  handleTabDragStart(e, tabElement) {
    this.draggedTab = tabElement;
    this.draggedData = {
      tabId: tabElement.dataset.tabId,
      sourceWindowId: this.getTabWindowId(tabElement)
    };

    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', JSON.stringify(this.draggedData));
    
    tabElement.classList.add('dragging');
  }

  handleTabDrop(targetWindowId) {
    if (this.draggedData.sourceWindowId !== targetWindowId) {
      // 移动标签到新窗口
      chrome.tabs.move(parseInt(this.draggedData.tabId), {
        windowId: parseInt(targetWindowId),
        index: -1
      });
    }

    this.draggedTab.classList.remove('dragging');
    this.draggedTab = null;
    this.draggedData = null;
  }

  getTabWindowId(tabElement) {
    // 从UI结构中获取标签所属的窗口ID
    const windowSection = tabElement.closest('[data-window-id]');
    return windowSection ? windowSection.dataset.windowId : null;
  }
}
```

### 4.3 键盘快捷键

```javascript
class KeyboardShortcuts {
  constructor() {
    this.shortcuts = {
      'Ctrl+A': () => this.selectAll(),
      'Ctrl+D': () => this.deselectAll(),
      'Delete': () => this.closeSelected(),
      'Ctrl+F': () => this.focusSearch(),
      'Ctrl+S': () => this.saveSession(),
      'Escape': () => this.cancelOperation()
    };
  }

  init() {
    document.addEventListener('keydown', (e) => {
      const key = this.getKeyCombo(e);
      const handler = this.shortcuts[key];
      
      if (handler) {
        e.preventDefault();
        handler();
      }
    });
  }

  getKeyCombo(e) {
    const keys = [];
    if (e.ctrlKey) keys.push('Ctrl');
    if (e.shiftKey) keys.push('Shift');
    if (e.altKey) keys.push('Alt');
    if (e.metaKey) keys.push('Cmd');
    
    if (e.key && e.key !== 'Control' && e.key !== 'Shift' && e.key !== 'Alt' && e.key !== 'Meta') {
      keys.push(e.key.length === 1 ? e.key.toUpperCase() : e.key);
    }
    
    return keys.join('+');
  }

  selectAll() {
    document.querySelectorAll('.tab-select').forEach(checkbox => {
      checkbox.checked = true;
    });
    document.dispatchEvent(new CustomEvent('selectionChanged'));
  }

  deselectAll() {
    document.querySelectorAll('.tab-select').forEach(checkbox => {
      checkbox.checked = false;
    });
    document.dispatchEvent(new CustomEvent('selectionChanged'));
  }

  closeSelected() {
    const selected = Array.from(document.querySelectorAll('.tab-select:checked'))
      .map(checkbox => parseInt(checkbox.dataset.tabId));
    
    if (selected.length > 0 && confirm(`确定要关闭 ${selected.length} 个标签页吗？`)) {
      chrome.tabs.remove(selected);
    }
  }

  focusSearch() {
    const searchBox = document.querySelector('.search-box');
    if (searchBox) {
      searchBox.focus();
      searchBox.select();
    }
  }
}
```

## 5. 视图模式

### 5.1 视图模式样式

```css
/* 卡片视图 */
.view-card .tabs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
}

.view-card .tab-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.view-card .tab-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 列表视图 */
.view-list .tabs-container {
  padding: 10px;
}

.view-list .tab-list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background: white;
}

.view-list .tab-list-item:hover {
  background: #f5f5f5;
}

/* 紧凑视图 */
.view-compact .tabs-container {
  padding: 10px;
}

.view-compact .tab-compact-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.view-compact .tab-favicon-small {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
```

## 6. 技术方案

### 6.1 模块化架构

```javascript
// 主应用类
class MyTab3App {
  constructor() {
    this.components = {
      windowList: null,
      toolbar: null,
      settings: null,
      tabsView: null
    };
    
    this.state = {
      windows: [],
      tabs: [],
      viewMode: 'card',
      searchQuery: '',
      selectedTabs: new Set()
    };
  }

  async init() {
    // 初始化组件
    this.components.windowList = new WindowList(
      document.querySelector('.window-list-container')
    );
    
    this.components.toolbar = new Toolbar(
      document.querySelector('.toolbar')
    );
    
    this.components.tabsView = new TabsView(
      document.querySelector('.tabs-container')
    );
    
    // 加载数据
    await this.loadData();
    
    // 绑定事件
    this.bindEvents();
    
    // 渲染界面
    this.render();
  }

  async loadData() {
    // 获取所有窗口和标签
    const windows = await chrome.windows.getAll({ populate: true });
    this.state.windows = windows;
    this.state.tabs = windows.flatMap(w => w.tabs);
  }

  bindEvents() {
    // 监听自定义事件
    document.addEventListener('viewChange', (e) => {
      this.state.viewMode = e.detail.viewMode;
      this.render();
    });

    document.addEventListener('search', (e) => {
      this.state.searchQuery = e.detail.query;
      this.filterTabs();
    });

    // 监听Chrome API事件
    chrome.tabs.onCreated.addListener(() => this.loadData());
    chrome.tabs.onRemoved.addListener(() => this.loadData());
    chrome.tabs.onUpdated.addListener(() => this.loadData());
  }

  render() {
    // 渲染各个组件
    this.components.windowList.render(this.state.windows);
    this.components.tabsView.render(this.getFilteredTabs(), this.state.viewMode);
    this.updateStatusBar();
  }

  getFilteredTabs() {
    if (!this.state.searchQuery) {
      return this.state.tabs;
    }

    const query = this.state.searchQuery.toLowerCase();
    return this.state.tabs.filter(tab => 
      tab.title.toLowerCase().includes(query) ||
      tab.url.toLowerCase().includes(query)
    );
  }

  updateStatusBar() {
    document.querySelector('.tab-count').textContent = 
      `共 ${this.state.tabs.length} 个标签页`;
    document.querySelector('.window-count').textContent = 
      `${this.state.windows.length} 个窗口`;
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  const app = new MyTab3App();
  app.init();
});
```

### 6.2 虚拟滚动实现

```javascript
class VirtualScroller {
  constructor(container, items, renderItem) {
    this.container = container;
    this.items = items;
    this.renderItem = renderItem;
    
    this.itemHeight = 80; // 预估高度
    this.buffer = 5; // 缓冲区项目数
    this.scrollTop = 0;
    this.visibleStart = 0;
    this.visibleEnd = 0;
    
    this.init();
  }

  init() {
    // 创建滚动容器
    this.scroller = document.createElement('div');
    this.scroller.className = 'virtual-scroller';
    this.scroller.style.height = '100%';
    this.scroller.style.overflow = 'auto';
    
    // 创建内容容器
    this.content = document.createElement('div');
    this.content.className = 'virtual-content';
    this.content.style.height = `${this.items.length * this.itemHeight}px`;
    
    // 创建可见区域容器
    this.visible = document.createElement('div');
    this.visible.className = 'virtual-visible';
    
    this.content.appendChild(this.visible);
    this.scroller.appendChild(this.content);
    this.container.appendChild(this.scroller);
    
    // 绑定滚动事件
    this.scroller.addEventListener('scroll', () => this.handleScroll());
    
    // 初始渲染
    this.render();
  }

  handleScroll() {
    this.scrollTop = this.scroller.scrollTop;
    this.render();
  }

  render() {
    const scrollHeight = this.scroller.clientHeight;
    const visibleStart = Math.floor(this.scrollTop / this.itemHeight);
    const visibleEnd = Math.ceil((this.scrollTop + scrollHeight) / this.itemHeight);
    
    // 添加缓冲区
    this.visibleStart = Math.max(0, visibleStart - this.buffer);
    this.visibleEnd = Math.min(this.items.length - 1, visibleEnd + this.buffer);
    
    // 清空并重新渲染可见项目
    this.visible.innerHTML = '';
    this.visible.style.transform = `translateY(${this.visibleStart * this.itemHeight}px)`;
    
    for (let i = this.visibleStart; i <= this.visibleEnd; i++) {
      const item = this.items[i];
      if (item) {
        const element = this.renderItem(item);
        this.visible.appendChild(element);
      }
    }
  }

  updateItems(items) {
    this.items = items;
    this.content.style.height = `${this.items.length * this.itemHeight}px`;
    this.render();
  }
}
```

## 7. 样式规范

### 7.1 颜色方案

```css
:root {
  /* 主色调 */
  --primary-color: #1a73e8;
  --primary-hover: #1557b0;
  --primary-light: #e8f0fe;
  
  /* 文本颜色 */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  
  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-hover: #f1f3f4;
  
  /* 边框颜色 */
  --border-color: #dadce0;
  --border-hover: #d2d2d2;
  
  /* 状态颜色 */
  --success: #1e8e3e;
  --warning: #f9ab00;
  --error: #d93025;
  --info: #1a73e8;
}
```

### 7.2 字体规范

```css
:root {
  /* 字体家族 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 'Helvetica Neue', Arial, sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;
}
```

### 7.3 间距规则

```css
:root {
  /* 间距尺寸 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 9999px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-md: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-lg: 0 4px 16px rgba(0,0,0,0.15);
  --shadow-xl: 0 8px 32px rgba(0,0,0,0.2);
}
```

### 7.4 动画效果

```css
/* 过渡动画 */
.transition {
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition-duration: 150ms;
}

.transition-slow {
  transition-duration: 300ms;
}

/* 淡入淡出 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* 滑动效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 缩放效果 */
@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 应用动画 */
.fade-enter {
  animation: fadeIn 200ms ease-out;
}

.fade-exit {
  animation: fadeOut 200ms ease-in;
}

.slide-enter {
  animation: slideInRight 300ms ease-out;
}

.slide-exit {
  animation: slideOutRight 300ms ease-in;
}

.scale-enter {
  animation: scaleIn 200ms ease-out;
}
```

## 8. 性能优化

### 8.1 懒加载图片

```javascript
class LazyImageLoader {
  constructor() {
    this.imageObserver = null;
    this.init();
  }

  init() {
    this.imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          this.loadImage(img);
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px'
    });
  }

  observe(img) {
    if ('IntersectionObserver' in window) {
      this.imageObserver.observe(img);
    } else {
      // 降级方案
      this.loadImage(img);
    }
  }

  loadImage(img) {
    const src = img.dataset.src;
    if (src) {
      img.src = src;
      img.onload = () => {
        img.classList.add('loaded');
      };
      img.onerror = () => {
        img.src = 'default-icon.png';
      };
    }
  }
}
```

### 8.2 防抖和节流

```javascript
// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 使用示例
const searchHandler = debounce((query) => {
  performSearch(query);
}, 300);

const scrollHandler = throttle(() => {
  updateScrollPosition();
}, 100);
```

## 9. 总结

MyTab3 UI界面模块采用模块化、组件化的设计理念，通过原生HTML/CSS/JavaScript实现高性能的标签页管理界面。主要特点包括：

1. **完整的网页式管理界面**：提供宽敞的操作空间
2. **响应式设计**：适配各种屏幕尺寸
3. **多种视图模式**：满足不同用户偏好
4. **丰富的交互功能**：拖拽、快捷键、右键菜单等
5. **高性能渲染**：虚拟滚动、懒加载等优化技术
6. **模块化架构**：易于维护和扩展

通过精心设计的UI组件和交互逻辑，MyTab3提供了直观、高效的标签页管理体验。