/**
 * 标签页API接口实现
 * 提供标签页数据的CRUD操作接口
 */

import { getStorage, setStorage } from '../utils/storage.js';
import { v4 as uuidv4 } from '../utils/uuid.js';

// 存储键
const TABS_STORAGE_KEY = 'mytab_tabs';

class TabAPI extends EventEmitter {
  constructor(storageManager = null) {
    super();
    this.indexer = new TabIndexer();
    this.storageManager = storageManager;
    this.initialized = false;
  }
  
  /**
   * 初始化API，加载已有数据
   */
  async initialize() {
    if (this.initialized) return;
    
    try {
      if (this.storageManager) {
        // 从存储加载所有标签页
        const tabs = await this.storageManager.getAllTabs();
        for (const tab of tabs) {
          this.indexer.addTab(tab);
        }
      }
      this.initialized = true;
    } catch (error) {
      throw new StorageError(`Failed to initialize TabAPI: ${error.message}`);
    }
  }
  
  /**
   * 创建单个标签页
   * @param {Object} tabData - 标签页数据
   * @returns {Promise<Object>} 创建的标签页对象
   */
  async createTab(tabData) {
    try {
      // 验证输入数据
      validateTabForCreate(tabData);
      
      // 准备完整的标签页数据
      const now = Date.now();
      const tab = {
        id: uuidv4(),
        windowId: tabData.windowId,
        url: tabData.url,
        title: tabData.title || tabData.url,
        favIconUrl: tabData.favIconUrl || '',
        index: tabData.index,
        createdAt: now,
        updatedAt: now,
        lastAccessedAt: now,
        tags: tabData.tags || [],
        note: tabData.note || '',
        isPinned: tabData.isPinned || false,
        groupId: tabData.groupId || null
      };
      
      // 验证完整数据
      validateCompleteTab(tab);
      
      // 检查ID是否重复
      if (this.indexer.getTab(tab.id)) {
        throw new DuplicateError(`Tab with ID ${tab.id} already exists`);
      }
      
      // 保存到存储
      if (this.storageManager) {
        await this.storageManager.saveTab(tab);
      }
      
      // 添加到索引
      this.indexer.addTab(tab);
      
      // 触发事件
      this.emit('tab:created', {
        type: 'tab:created',
        timestamp: Date.now(),
        data: { tab }
      });
      
      return tab;
    } catch (error) {
      if (error instanceof ValidationError || 
          error instanceof DuplicateError ||
          error instanceof StorageError) {
        throw error;
      }
      throw new StorageError(`Failed to create tab: ${error.message}`);
    }
  }
  
  /**
   * 更新标签页
   * @param {string} id - 标签页ID
   * @param {Object} updates - 更新数据
   * @returns {Promise<Object>} 更新后的标签页对象
   */
  async updateTab(id, updates) {
    try {
      // 验证更新数据
      validateTabUpdates(updates);
      
      // 获取现有标签页
      const existingTab = this.indexer.getTab(id);
      if (!existingTab) {
        throw new NotFoundError(`Tab with ID ${id} not found`);
      }
      
      // 准备更新数据
      const updateData = {
        ...updates,
        updatedAt: Date.now()
      };
      
      // 如果URL改变，更新lastAccessedAt
      if (updates.url && updates.url !== existingTab.url) {
        updateData.lastAccessedAt = updateData.updatedAt;
      }
      
      // 创建更新后的完整标签页对象
      const updatedTab = { ...existingTab, ...updateData };
      
      // 验证更新后的数据
      validateCompleteTab(updatedTab);
      
      // 保存到存储
      if (this.storageManager) {
        await this.storageManager.updateTab(id, updatedTab);
      }
      
      // 更新索引
      const result = this.indexer.updateTab(id, updateData);
      
      // 触发事件
      this.emit('tab:updated', {
        type: 'tab:updated',
        timestamp: Date.now(),
        data: { 
          tab: result,
          updates: updateData
        }
      });
      
      return result;
    } catch (error) {
      if (error instanceof ValidationError || 
          error instanceof NotFoundError) {
        throw error;
      }
      throw new StorageError(`Failed to update tab: ${error.message}`);
    }
  }
  
  /**
   * 删除标签页
   * @param {string} id - 标签页ID
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteTab(id) {
    try {
      // 检查标签页是否存在
      const tab = this.indexer.getTab(id);
      if (!tab) {
        throw new NotFoundError(`Tab with ID ${id} not found`);
      }
      
      // 从存储删除
      if (this.storageManager) {
        await this.storageManager.deleteTab(id);
      }
      
      // 从索引删除
      this.indexer.removeTab(id);
      
      // 触发事件
      this.emit('tab:deleted', {
        type: 'tab:deleted',
        timestamp: Date.now(),
        data: { id, tab }
      });
      
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new StorageError(`Failed to delete tab: ${error.message}`);
    }
  }
  
  /**
   * 获取单个标签页
   * @param {string} id - 标签页ID
   * @returns {Promise<Object|null>} 标签页对象或null
   */
  async getTab(id) {
    return this.indexer.getTab(id);
  }
  
  /**
   * 查询标签页列表
   * @param {Object} filter - 过滤条件
   * @returns {Promise<Object[]>} 标签页数组
   */
  async getTabs(filter = {}) {
    try {
      // 验证过滤条件
      validateTabFilter(filter);
      
      // 查询索引
      return this.indexer.queryTabs(filter);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new StorageError(`Failed to query tabs: ${error.message}`);
    }
  }
  
  /**
   * 批量创建标签页
   * @param {Object[]} tabs - 标签页数据数组
   * @returns {Promise<Object[]>} 创建的标签页数组
   */
  async batchCreateTabs(tabs) {
    if (!Array.isArray(tabs) || tabs.length === 0) {
      throw new ValidationError('tabs must be a non-empty array');
    }
    
    // 限制批量操作数量
    if (tabs.length > 1000) {
      throw new ValidationError('Cannot create more than 1000 tabs at once');
    }
    
    const createdTabs = [];
    const errors = [];
    
    try {
      // 开始事务（如果存储支持）
      if (this.storageManager && this.storageManager.beginTransaction) {
        await this.storageManager.beginTransaction();
      }
      
      // 验证所有数据
      for (let i = 0; i < tabs.length; i++) {
        try {
          validateTabForCreate(tabs[i]);
        } catch (error) {
          errors.push({ index: i, error: error.message });
        }
      }
      
      if (errors.length > 0) {
        throw new ValidationError(`Validation failed for ${errors.length} tabs: ${JSON.stringify(errors)}`);
      }
      
      // 创建所有标签页
      for (const tabData of tabs) {
        const tab = await this.createTab(tabData);
        createdTabs.push(tab);
      }
      
      // 提交事务
      if (this.storageManager && this.storageManager.commitTransaction) {
        await this.storageManager.commitTransaction();
      }
      
      // 触发批量创建事件
      this.emit('tab:batch-created', {
        type: 'tab:batch-created',
        timestamp: Date.now(),
        data: { tabs: createdTabs }
      });
      
      return createdTabs;
    } catch (error) {
      // 回滚事务
      if (this.storageManager && this.storageManager.rollbackTransaction) {
        await this.storageManager.rollbackTransaction();
      }
      
      // 回滚已创建的标签页
      for (const tab of createdTabs) {
        this.indexer.removeTab(tab.id);
      }
      
      throw error;
    }
  }
  
  /**
   * 批量更新标签页
   * @param {Object[]} updates - 更新操作数组
   * @returns {Promise<Object[]>} 更新后的标签页数组
   */
  async batchUpdateTabs(updates) {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw new ValidationError('updates must be a non-empty array');
    }
    
    // 限制批量操作数量
    if (updates.length > 1000) {
      throw new ValidationError('Cannot update more than 1000 tabs at once');
    }
    
    const updatedTabs = [];
    const originalStates = new Map();
    
    try {
      // 开始事务
      if (this.storageManager && this.storageManager.beginTransaction) {
        await this.storageManager.beginTransaction();
      }
      
      // 验证所有更新
      for (const update of updates) {
        if (!update.id) {
          throw new ValidationError('Each update must have an id field');
        }
        
        const existingTab = this.indexer.getTab(update.id);
        if (!existingTab) {
          throw new NotFoundError(`Tab with ID ${update.id} not found`);
        }
        
        originalStates.set(update.id, existingTab);
        validateTabUpdates(update.updates);
      }
      
      // 执行所有更新
      for (const update of updates) {
        const updatedTab = await this.updateTab(update.id, update.updates);
        updatedTabs.push(updatedTab);
      }
      
      // 提交事务
      if (this.storageManager && this.storageManager.commitTransaction) {
        await this.storageManager.commitTransaction();
      }
      
      // 触发批量更新事件
      this.emit('tab:batch-updated', {
        type: 'tab:batch-updated',
        timestamp: Date.now(),
        data: { tabs: updatedTabs }
      });
      
      return updatedTabs;
    } catch (error) {
      // 回滚事务
      if (this.storageManager && this.storageManager.rollbackTransaction) {
        await this.storageManager.rollbackTransaction();
      }
      
      // 回滚已更新的标签页
      for (const [id, originalTab] of originalStates) {
        this.indexer.updateTab(id, originalTab);
      }
      
      throw error;
    }
  }
  
  /**
   * 批量删除标签页
   * @param {string[]} ids - 标签页ID数组
   * @returns {Promise<boolean>} 是否成功删除
   */
  async batchDeleteTabs(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new ValidationError('ids must be a non-empty array');
    }
    
    // 限制批量操作数量
    if (ids.length > 1000) {
      throw new ValidationError('Cannot delete more than 1000 tabs at once');
    }
    
    const deletedTabs = [];
    
    try {
      // 开始事务
      if (this.storageManager && this.storageManager.beginTransaction) {
        await this.storageManager.beginTransaction();
      }
      
      // 检查所有标签页是否存在
      for (const id of ids) {
        const tab = this.indexer.getTab(id);
        if (!tab) {
          throw new NotFoundError(`Tab with ID ${id} not found`);
        }
        deletedTabs.push(tab);
      }
      
      // 删除所有标签页
      for (const id of ids) {
        await this.deleteTab(id);
      }
      
      // 提交事务
      if (this.storageManager && this.storageManager.commitTransaction) {
        await this.storageManager.commitTransaction();
      }
      
      // 触发批量删除事件
      this.emit('tab:batch-deleted', {
        type: 'tab:batch-deleted',
        timestamp: Date.now(),
        data: { ids, tabs: deletedTabs }
      });
      
      return true;
    } catch (error) {
      // 回滚事务
      if (this.storageManager && this.storageManager.rollbackTransaction) {
        await this.storageManager.rollbackTransaction();
      }
      
      // 恢复已删除的标签页
      for (const tab of deletedTabs) {
        this.indexer.addTab(tab);
      }
      
      throw error;
    }
  }
  
  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return this.indexer.getStats();
  }
  
  /**
   * 清空所有数据
   */
  async clear() {
    try {
      if (this.storageManager) {
        await this.storageManager.clearAllTabs();
      }
      
      this.indexer.clear();
      
      this.emit('tab:cleared', {
        type: 'tab:cleared',
        timestamp: Date.now(),
        data: {}
      });
    } catch (error) {
      throw new StorageError(`Failed to clear tabs: ${error.message}`);
    }
  }
}

// ES6 导出函数，用于简单的数据访问
export async function getAllTabs() {
  try {
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    return tabs;
  } catch (error) {
    console.error('Failed to get all tabs:', error);
    return [];
  }
}

export async function createTab(tabData) {
  try {
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    const newTab = {
      id: uuidv4(),
      windowId: tabData.windowId,
      url: tabData.url,
      title: tabData.title || tabData.url,
      favIconUrl: tabData.favIconUrl || '',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      ...tabData
    };

    tabs.push(newTab);
    await setStorage(TABS_STORAGE_KEY, tabs);
    return newTab;
  } catch (error) {
    console.error('Failed to create tab:', error);
    throw error;
  }
}

export async function updateTab(tabId, updates) {
  try {
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    const tabIndex = tabs.findIndex(t => t.id === tabId);

    if (tabIndex === -1) {
      throw new Error(`Tab not found: ${tabId}`);
    }

    tabs[tabIndex] = {
      ...tabs[tabIndex],
      ...updates,
      updatedAt: Date.now()
    };

    await setStorage(TABS_STORAGE_KEY, tabs);
    return tabs[tabIndex];
  } catch (error) {
    console.error('Failed to update tab:', error);
    throw error;
  }
}

export async function deleteTab(tabId) {
  try {
    const tabs = await getStorage(TABS_STORAGE_KEY) || [];
    const filteredTabs = tabs.filter(t => t.id !== tabId);
    await setStorage(TABS_STORAGE_KEY, filteredTabs);
    return true;
  } catch (error) {
    console.error('Failed to delete tab:', error);
    throw error;
  }
}

// 导出TabAPI类（如果需要的话）
export { TabAPI };