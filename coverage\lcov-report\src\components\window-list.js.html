
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/window-list.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/components</a> window-list.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/116</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/70</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/109</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * WindowList 组件
 * 用于显示和管理所有浏览器窗口
 */
class WindowList {
<span class="fstat-no" title="function not covered" >  co</span>nstructor(container) {
<span class="cstat-no" title="statement not covered" >    this.container = container;</span>
<span class="cstat-no" title="statement not covered" >    this.windows = [];</span>
<span class="cstat-no" title="statement not covered" >    this.selectedWindowId = null;</span>
<span class="cstat-no" title="statement not covered" >    this.draggedElement = null;</span>
<span class="cstat-no" title="statement not covered" >    this.onWindowSelect = null;</span>
<span class="cstat-no" title="statement not covered" >    this.onWindowUpdate = null;</span>
  }
&nbsp;
  /**
   * 设置窗口数据
   */
<span class="fstat-no" title="function not covered" >  se</span>tWindows(windows) {
<span class="cstat-no" title="statement not covered" >    this.windows = windows;</span>
<span class="cstat-no" title="statement not covered" >    this.render();</span>
  }
&nbsp;
  /**
   * 选择窗口
   */
<span class="fstat-no" title="function not covered" >  se</span>lectWindow(windowId) {
<span class="cstat-no" title="statement not covered" >    this.selectedWindowId = windowId;</span>
<span class="cstat-no" title="statement not covered" >    this.render();</span>
<span class="cstat-no" title="statement not covered" >    if (this.onWindowSelect) {</span>
<span class="cstat-no" title="statement not covered" >      this.onWindowSelect(windowId);</span>
    }
  }
&nbsp;
  /**
   * 渲染窗口列表
   */
<span class="fstat-no" title="function not covered" >  re</span>nder() {
    const html = <span class="cstat-no" title="statement not covered" >this.windows.map(<span class="fstat-no" title="function not covered" >wi</span>ndow =&gt; <span class="cstat-no" title="statement not covered" >this.renderWindow(window))</span>.join('');</span>
<span class="cstat-no" title="statement not covered" >    this.container.innerHTML = html;</span>
<span class="cstat-no" title="statement not covered" >    this.attachEventListeners();</span>
  }
&nbsp;
  /**
   * 渲染单个窗口
   */
<span class="fstat-no" title="function not covered" >  re</span>nderWindow(window) {
    const isSelected = <span class="cstat-no" title="statement not covered" >window.id === this.selectedWindowId;</span>
    const isCollapsed = <span class="cstat-no" title="statement not covered" >window.collapsed || false;</span>
    const tabCount = <span class="cstat-no" title="statement not covered" >window.tabs ? window.tabs.length : 0;</span>
    const title = <span class="cstat-no" title="statement not covered" >this.getWindowTitle(window);</span>
    
<span class="cstat-no" title="statement not covered" >    return `</span>
      &lt;div class="window-item ${isSelected ? 'selected' : ''}" 
           data-window-id="${window.id}"
           draggable="true"&gt;
        &lt;div class="window-header"&gt;
          &lt;span class="window-toggle ${isCollapsed ? 'collapsed' : ''}"&gt;▼&lt;/span&gt;
          &lt;span class="window-icon"&gt;🪟&lt;/span&gt;
          &lt;span class="window-title" title="${title}"&gt;${title}&lt;/span&gt;
          &lt;span class="window-tab-count"&gt;${tabCount}&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="window-tabs ${isCollapsed ? 'hidden' : ''}"&gt;
          ${this.renderWindowTabs(window.tabs || [])}
        &lt;/div&gt;
      &lt;/div&gt;
    `;
  }
&nbsp;
  /**
   * 获取窗口标题
   */
<span class="fstat-no" title="function not covered" >  ge</span>tWindowTitle(window) {
<span class="cstat-no" title="statement not covered" >    if (window.customName) {</span>
<span class="cstat-no" title="statement not covered" >      return window.customName;</span>
    }
    
    // 如果有聚焦的标签页，使用其标题
<span class="cstat-no" title="statement not covered" >    if (window.tabs &amp;&amp; window.tabs.length &gt; 0) {</span>
      const activeTab = <span class="cstat-no" title="statement not covered" >window.tabs.find(<span class="fstat-no" title="function not covered" >ta</span>b =&gt; <span class="cstat-no" title="statement not covered" >tab.active)</span>;</span>
<span class="cstat-no" title="statement not covered" >      if (activeTab) {</span>
<span class="cstat-no" title="statement not covered" >        return activeTab.title || `窗口 ${window.id}`;</span>
      }
    }
    
<span class="cstat-no" title="statement not covered" >    return `窗口 ${window.id}`;</span>
  }
&nbsp;
  /**
   * 渲染窗口内的标签页预览
   */
<span class="fstat-no" title="function not covered" >  re</span>nderWindowTabs(tabs) {
<span class="cstat-no" title="statement not covered" >    if (!tabs || tabs.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >      return '&lt;div class="window-tab-preview empty"&gt;没有标签页&lt;/div&gt;';</span>
    }
    
    // 最多显示5个标签页预览
    const previewTabs = <span class="cstat-no" title="statement not covered" >tabs.slice(0, 5);</span>
    const previews = <span class="cstat-no" title="statement not covered" >previewTabs.map(<span class="fstat-no" title="function not covered" >ta</span>b =&gt; <span class="cstat-no" title="statement not covered" >`</span></span>
      &lt;div class="window-tab-preview" data-tab-id="${tab.id}"&gt;
        &lt;img src="${tab.favIconUrl || '../assets/default-icon.png'}" alt="" 
             onerror="this.src='../assets/default-icon.png'"&gt;
        &lt;span&gt;${tab.title || '无标题'}&lt;/span&gt;
      &lt;/div&gt;
    `).join('');
    
    // 如果还有更多标签页，显示提示
    const moreCount = <span class="cstat-no" title="statement not covered" >tabs.length - previewTabs.length;</span>
    const moreHtml = <span class="cstat-no" title="statement not covered" >moreCount &gt; 0 ? </span>
      `&lt;div class="window-tab-preview more"&gt;还有 ${moreCount} 个标签页...&lt;/div&gt;` : '';
    
<span class="cstat-no" title="statement not covered" >    return previews + moreHtml;</span>
  }
&nbsp;
  /**
   * 绑定事件监听器
   */
<span class="fstat-no" title="function not covered" >  at</span>tachEventListeners() {
    // 窗口选择
<span class="cstat-no" title="statement not covered" >    this.container.querySelectorAll('.window-item').forEach(<span class="fstat-no" title="function not covered" >it</span>em =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      item.addEventListener('click', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!e.target.closest('.window-toggle')) {</span>
          const windowId = <span class="cstat-no" title="statement not covered" >parseInt(item.dataset.windowId);</span>
<span class="cstat-no" title="statement not covered" >          this.selectWindow(windowId);</span>
        }
      });
    });
&nbsp;
    // 折叠/展开
<span class="cstat-no" title="statement not covered" >    this.container.querySelectorAll('.window-toggle').forEach(<span class="fstat-no" title="function not covered" >to</span>ggle =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      toggle.addEventListener('click', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.stopPropagation();</span>
        const windowItem = <span class="cstat-no" title="statement not covered" >e.target.closest('.window-item');</span>
        const windowId = <span class="cstat-no" title="statement not covered" >parseInt(windowItem.dataset.windowId);</span>
<span class="cstat-no" title="statement not covered" >        this.toggleWindow(windowId);</span>
      });
    });
&nbsp;
    // 拖拽排序
<span class="cstat-no" title="statement not covered" >    this.initDragAndDrop();</span>
  }
&nbsp;
  /**
   * 切换窗口折叠状态
   */
<span class="fstat-no" title="function not covered" >  to</span>ggleWindow(windowId) {
    const window = <span class="cstat-no" title="statement not covered" >this.windows.find(<span class="fstat-no" title="function not covered" >w </span>=&gt; <span class="cstat-no" title="statement not covered" >w.id === windowId)</span>;</span>
<span class="cstat-no" title="statement not covered" >    if (window) {</span>
<span class="cstat-no" title="statement not covered" >      window.collapsed = !window.collapsed;</span>
<span class="cstat-no" title="statement not covered" >      this.render();</span>
<span class="cstat-no" title="statement not covered" >      if (this.onWindowUpdate) {</span>
<span class="cstat-no" title="statement not covered" >        this.onWindowUpdate(window);</span>
      }
    }
  }
&nbsp;
  /**
   * 初始化拖拽功能
   */
<span class="fstat-no" title="function not covered" >  in</span>itDragAndDrop() {
    const items = <span class="cstat-no" title="statement not covered" >this.container.querySelectorAll('.window-item');</span>
    
<span class="cstat-no" title="statement not covered" >    items.forEach(<span class="fstat-no" title="function not covered" >it</span>em =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      item.addEventListener('dragstart', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        this.draggedElement = e.target;</span>
<span class="cstat-no" title="statement not covered" >        e.target.classList.add('dragging');</span>
<span class="cstat-no" title="statement not covered" >        e.dataTransfer.effectAllowed = 'move';</span>
<span class="cstat-no" title="statement not covered" >        e.dataTransfer.setData('text/html', e.target.innerHTML);</span>
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      item.addEventListener('dragend', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.target.classList.remove('dragging');</span>
<span class="cstat-no" title="statement not covered" >        this.draggedElement = null;</span>
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      item.addEventListener('dragover', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >        if (!this.draggedElement) <span class="cstat-no" title="statement not covered" >return;</span></span>
        
        const afterElement = <span class="cstat-no" title="statement not covered" >this.getDragAfterElement(e.clientY);</span>
<span class="cstat-no" title="statement not covered" >        if (afterElement == null) {</span>
<span class="cstat-no" title="statement not covered" >          this.container.appendChild(this.draggedElement);</span>
        } else {
<span class="cstat-no" title="statement not covered" >          this.container.insertBefore(this.draggedElement, afterElement);</span>
        }
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      item.addEventListener('drop', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >        this.updateWindowOrder();</span>
      });
    });
  }
&nbsp;
  /**
   * 获取拖拽后的位置元素
   */
<span class="fstat-no" title="function not covered" >  ge</span>tDragAfterElement(y) {
    const draggableElements = <span class="cstat-no" title="statement not covered" >[...this.container.querySelectorAll('.window-item:not(.dragging)')];</span>
    
<span class="cstat-no" title="statement not covered" >    return draggableElements.reduce(<span class="fstat-no" title="function not covered" >(c</span>losest, child) =&gt; {</span>
      const box = <span class="cstat-no" title="statement not covered" >child.getBoundingClientRect();</span>
      const offset = <span class="cstat-no" title="statement not covered" >y - box.top - box.height / 2;</span>
      
<span class="cstat-no" title="statement not covered" >      if (offset &lt; 0 &amp;&amp; offset &gt; closest.offset) {</span>
<span class="cstat-no" title="statement not covered" >        return { offset: offset, element: child };</span>
      } else {
<span class="cstat-no" title="statement not covered" >        return closest;</span>
      }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
  }
&nbsp;
  /**
   * 更新窗口顺序
   */
<span class="fstat-no" title="function not covered" >  up</span>dateWindowOrder() {
    const newOrder = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >    this.container.querySelectorAll('.window-item').forEach(<span class="fstat-no" title="function not covered" >it</span>em =&gt; {</span>
      const windowId = <span class="cstat-no" title="statement not covered" >parseInt(item.dataset.windowId);</span>
      const window = <span class="cstat-no" title="statement not covered" >this.windows.find(<span class="fstat-no" title="function not covered" >w </span>=&gt; <span class="cstat-no" title="statement not covered" >w.id === windowId)</span>;</span>
<span class="cstat-no" title="statement not covered" >      if (window) {</span>
<span class="cstat-no" title="statement not covered" >        newOrder.push(window);</span>
      }
    });
    
<span class="cstat-no" title="statement not covered" >    this.windows = newOrder;</span>
<span class="cstat-no" title="statement not covered" >    if (this.onWindowUpdate) {</span>
<span class="cstat-no" title="statement not covered" >      this.onWindowUpdate(null, this.windows);</span>
    }
  }
&nbsp;
  /**
   * 搜索窗口
   */
<span class="fstat-no" title="function not covered" >  se</span>arch(query) {
<span class="cstat-no" title="statement not covered" >    if (!query) {</span>
<span class="cstat-no" title="statement not covered" >      this.container.querySelectorAll('.window-item').forEach(<span class="fstat-no" title="function not covered" >it</span>em =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        item.style.display = '';</span>
      });
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
    
    const lowerQuery = <span class="cstat-no" title="statement not covered" >query.toLowerCase();</span>
<span class="cstat-no" title="statement not covered" >    this.container.querySelectorAll('.window-item').forEach(<span class="fstat-no" title="function not covered" >it</span>em =&gt; {</span>
      const windowId = <span class="cstat-no" title="statement not covered" >parseInt(item.dataset.windowId);</span>
      const window = <span class="cstat-no" title="statement not covered" >this.windows.find(<span class="fstat-no" title="function not covered" >w </span>=&gt; <span class="cstat-no" title="statement not covered" >w.id === windowId)</span>;</span>
      
<span class="cstat-no" title="statement not covered" >      if (window) {</span>
        const title = <span class="cstat-no" title="statement not covered" >this.getWindowTitle(window).toLowerCase();</span>
        const hasMatchingTab = <span class="cstat-no" title="statement not covered" >window.tabs &amp;&amp; window.tabs.some(<span class="fstat-no" title="function not covered" >ta</span>b =&gt; </span>
<span class="cstat-no" title="statement not covered" >          (tab.title &amp;&amp; tab.title.toLowerCase().includes(lowerQuery)) ||</span>
          (tab.url &amp;&amp; tab.url.toLowerCase().includes(lowerQuery))
        );
        
<span class="cstat-no" title="statement not covered" >        if (title.includes(lowerQuery) || hasMatchingTab) {</span>
<span class="cstat-no" title="statement not covered" >          item.style.display = '';</span>
        } else {
<span class="cstat-no" title="statement not covered" >          item.style.display = 'none';</span>
        }
      }
    });
  }
&nbsp;
  /**
   * 清理组件
   */
<span class="fstat-no" title="function not covered" >  de</span>stroy() {
<span class="cstat-no" title="statement not covered" >    this.container.innerHTML = '';</span>
<span class="cstat-no" title="statement not covered" >    this.windows = [];</span>
<span class="cstat-no" title="statement not covered" >    this.selectedWindowId = null;</span>
<span class="cstat-no" title="statement not covered" >    this.draggedElement = null;</span>
  }
}
&nbsp;
// 导出组件
<span class="cstat-no" title="statement not covered" >if (typeof module !== 'undefined' &amp;&amp; module.exports) {</span>
<span class="cstat-no" title="statement not covered" >  module.exports = WindowList;</span>
}</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-20T14:40:38.170Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    